<?php

declare(strict_types=1);

namespace App\Service\User;

use App\Models\User\User;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\GoodsInstructionImagesModel;
use App\Models\User\UserGoodsDirectoryModel;
use App\Service\User\MediaInfoService;
use App\Exceptions\MyException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * EXE商品服务类
 * 处理EXE程序相关的商品业务逻辑
 */
class ExeGoodsService
{
    protected MediaInfoService $mediaInfoService;
    const MIN_USER_ID_THRESHOLD = 0;

    public function __construct(MediaInfoService $mediaInfoService)
    {
        $this->mediaInfoService = $mediaInfoService;
    }

    /**
     * 验证用户是否有API访问权限
     *
     * @param User $user
     * @throws MyException
     */
    public function validateApiPermission(User $user): void
    {
        // 检查用户状态
        if ($user->status != 1) {
            throw new MyException('用户账号已被禁用', 403);
        }

        // 检查API状态
        if ($user->appstatus != 1) {
            throw new MyException('API功能已被禁用', 403);
        }
    }

    /**
     * 验证商品归属权
     *
     * @param GoodsModel $goods
     * @param User $user
     * @return bool
     * @throws MyException
     */
    public function validateGoodsOwnership(GoodsModel $goods, User $user): bool
    {
        // 判断用户类型并验证归属权
        if ($user->pid == 0) {
            // 主账号：检查user_id
            if ($goods->user_id != $user->id) {
                throw new MyException('无权限访问该商品', 403);
            }
        } else {
            // 子账号：检查user_sub_id
            if ($goods->user_sub_id != $user->id) {
                throw new MyException('无权限访问该商品', 403);
            }
        }

        return true;
    }

    /**
     * 检查用户是否为主账号
     *
     * @param User $user
     * @return bool
     */
    public function isMainAccount(User $user): bool
    {
        return $user->pid == 0;
    }

    /**
     * 检查用户是否为子账号
     *
     * @param User $user
     * @return bool
     */
    public function isSubAccount(User $user): bool
    {
        return $user->pid > 0;
    }

    /**
     * 根据用户类型构建商品查询条件
     *
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function buildGoodsQuery(User $user)
    {
        $query = GoodsModel::query();

        if ($user->pid == 0) {
            // 主账号：查询user_id
            $query->where('user_id', $user->id);
        } else {
            throw new MyException('您没有权限进行该操作', 401);
            // 子账号：查询user_sub_id
            // $query->where('user_sub_id', $user->id);
        }

        return $query;
    }

    /**
     * 获取用户的有效商品查询条件
     *
     * @param User $user
     * @param array $additionalConditions 额外的查询条件
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getUserGoodsQuery(User $user, array $additionalConditions = [])
    {
        $query = $this->buildGoodsQuery($user);

        // 添加额外条件
        foreach ($additionalConditions as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query;
    }

    /**
     * 获取待处理商品信息
     *
     * @param User $user
     * @return array|null
     */
    public function getPendingGoodsData(User $user,int $only_error): ?array
    {
        // 根据用户类型构建查询条件
        $query = $this->buildGoodsQuery($user);

        // 先获取待处理商品的总数
        $pendingTotalCount = $query->where('img_local_status', 0)
                                  ->where('img_local_error', $only_error)
                                  ->where('status', 1)
                                  ->count();
        if ($pendingTotalCount == 0) {
            return null;
        }
        // 查询待处理的商品（img_local_status = 0 且 status = 1）
        $goods = $query->where('img_local_status', 0)
                      ->where('img_local_error', $only_error)
                      ->where('status', 1)
                      ->orderBy('id', 'desc')
                      ->first();

        if (!$goods) {
            return null;
        }

        // 获取商品的媒体文件信息
        $mediaFiles = $this->mediaInfoService->getPendingMediaFiles($goods);

        // 获取目录名称
        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'user_id'  => $user->id,
            'phone'    => $user->phone,
            'goods_id' => $goods->id,
            'goods_name' => $goods->goods_name,
            'goods_platform_id' => $goods->goods_id,
            'directory_name' => $directoryName,
            'media_files' => $mediaFiles,
            'pending_total_count' => $pendingTotalCount
        ];
    }

    /**
     * 处理文件上传
     *
     * @param User $user
     * @param int $goodsId
     * @param string $fileType
     * @param string $fieldName
     * @param UploadedFile $uploadedFile
     * @param string $originalUrl
     * @return array
     * @throws MyException
     */
    public function handleFileUpload(User $user, int $goodsId, string $fileType, string $fieldType, string $fieldName, UploadedFile $uploadedFile, string $originalUrl): array
    {
        // 查询商品并验证归属权
        $query = $this->buildGoodsQuery($user);
        $goods = $query->where('id', $goodsId)->first();

        if (!$goods) {
            throw new MyException('商品不存在', 404);
        }

        $this->validateGoodsOwnership($goods, $user);

        // 提前获取文件大小，避免后续多次访问临时文件
        try {
            $fileSize = $uploadedFile->getSize();
        } catch (\Exception $e) {
            throw new MyException('无法获取文件大小：' . $e->getMessage(), 422);
        }

        // 验证文件类型和大小
        $this->validateUploadedFile($uploadedFile, $fileType);

        // 验证原始URL是否属于该商品的待处理文件
        $this->validateOriginalUrl($goods, $originalUrl, $fieldName);

        // 存储文件
        $localPath = $this->storeUploadedFile($uploadedFile, $user->id, $goods->goods_id, $fileType, $fieldType, $originalUrl);

        if (!$localPath) {
            throw new MyException('文件存储失败', 500);
        }

        return [
            'message' => '文件上传成功',
            'local_path' => $localPath,
            'original_url' => $originalUrl,
            'file_size' => $fileSize
        ];
    }

    /**
     * 更新商品状态
     *
     * @param User $user
     * @param int $goodsId
     * @param array $fieldUpdates
     * @param bool $markCompleted
     * @return array
     * @throws MyException
     */
    public function updateGoodsStatus(User $user, int $goodsId, array $fieldUpdates, bool $markCompleted = false): array
    {
        // 验证用户权限
        $this->validateApiPermission($user);

        // 查询商品并验证归属权
        $query = $this->buildGoodsQuery($user);
        $goods = $query->where('id', $goodsId)->first();

        if (!$goods) {
            throw new MyException('商品不存在', 404);
        }

        $this->validateGoodsOwnership($goods, $user);

        // 验证商品状态--不需要验证状态
        //$this->validateGoodsStatus($goods);

        // 验证字段更新数据
        $this->validateFieldUpdates($fieldUpdates);

        // 验证关联数据的存在性--不需要验证
        //$this->validateRelatedData($goods, $fieldUpdates);

        // 使用数据库事务确保数据一致性
        return DB::transaction(function () use ($goods, $fieldUpdates, $markCompleted, $goodsId) {
            try {
                // 更新商品字段
                $updatedFields = $this->updateGoodsFields($goods, $fieldUpdates);

                // 标记处理完成
                if ($markCompleted) {
                    $goods->img_local_status = 1;
                    $goods->img_local_error = 0;
                    $goods->updated_at = now();
                    $goods->save();
                }

                return [
                    'message' => '商品状态更新成功',
                    'goods_id' => $goodsId,
                    'updated_fields' => $updatedFields,
                    'img_local_status' => $goods->img_local_status,
                    'updated_at' => $goods->updated_at
                ];

            } catch (\Exception $e) {
                Log::error('商品状态更新失败', [
                    'goods_id' => $goodsId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new MyException('商品状态更新失败: ' . $e->getMessage(), 500);
            }
        });
    }

    /**
     * 验证商品状态--暂时不用这个逻辑
     *
     * @param GoodsModel $goods
     * @throws MyException
     */
    private function validateGoodsStatus(GoodsModel $goods): void
    {
        // 检查商品是否已被删除或禁用
        /* if ($goods->status != 1) {
            throw new MyException('商品状态异常，无法更新', 422);
        } */

        // 检查商品是否已经处理完成
        if ($goods->img_local_status == 1) {
            Log::warning('尝试更新已完成的商品', [
                'goods_id' => $goods->id,
                'img_local_status' => $goods->img_local_status
            ]);
            // 允许重新处理，但记录警告
        }
    }

    /**
     * 验证关联数据的存在性--暂时不用这个逻辑
     *
     * @param GoodsModel $goods
     * @param array $fieldUpdates
     * @throws MyException
     */
    private function validateRelatedData(GoodsModel $goods, array $fieldUpdates): void
    {
        // 验证 instruction_images 的 model_id 是否存在
        if (isset($fieldUpdates['instruction_images'])) {
            $modelIds = [];
            foreach ($fieldUpdates['instruction_images'] as $item) {
                if (isset($item['model_id'])) {
                    $modelIds[] = $item['model_id'];
                }
            }

            if (!empty($modelIds)) {
                $existingModels = GoodsInstructionImagesModel::whereIn('id', $modelIds)
                    ->where('user_goods_id', $goods->id)
                    ->pluck('id')
                    ->toArray();

                $missingModels = array_diff($modelIds, $existingModels);
                if (!empty($missingModels)) {
                    throw new MyException('说明图片记录不存在: ' . implode(', ', $missingModels), 422);
                }
            }
        }

        // 验证 SKU 的 sku_id 是否存在
        $skuFields = ['sku_thumb', 'sku_skc_gallery'];
        foreach ($skuFields as $field) {
            if (isset($fieldUpdates[$field])) {
                $skuIds = [];
                foreach ($fieldUpdates[$field] as $item) {
                    if (isset($item['sku_id'])) {
                        $skuIds[] = $item['sku_id'];
                    }
                }

                if (!empty($skuIds)) {
                    $existingSkus = GoodsSkuModel::whereIn('id', $skuIds)
                        ->where('user_goods_id', $goods->id)
                        ->pluck('id')
                        ->toArray();

                    $missingSkus = array_diff($skuIds, $existingSkus);
                    if (!empty($missingSkus)) {
                        throw new MyException("SKU记录不存在: " . implode(', ', $missingSkus), 422);
                    }
                }
            }
        }
    }

    /**
     * 验证上传的文件
     *
     * @param UploadedFile $file
     * @param string $fileType
     * @throws MyException
     */
    private function validateUploadedFile(UploadedFile $file, string $fileType): void
    {
        // 定义文件验证规则
        $rules = [
            'image' => [
                'mimes' => ['jpeg', 'jpg', 'png', 'gif', 'webp'],
                'max_size' => 10 * 1024 * 1024 // 10MB
            ],
            'video' => [
                'mimes' => ['mp4', 'avi', 'mov', 'wmv'],
                'max_size' => 100 * 1024 * 1024 // 100MB
            ],
            'pdf' => [
                'mimes' => ['pdf'],
                'max_size' => 100 * 1024 * 1024 // 100MB
            ]
        ];

        if (!isset($rules[$fileType])) {
            throw new MyException('不支持的文件类型', 422);
        }

        $rule = $rules[$fileType];

        // 验证文件是否有效
        if (!$file->isValid()) {
            throw new MyException('上传的文件无效', 422);
        }

        // 一次性获取文件大小，避免多次访问临时文件
        try {
            $fileSize = $file->getSize();
        } catch (\Exception $e) {
            throw new MyException('无法获取文件大小：' . $e->getMessage(), 422);
        }

        // 验证文件大小
        if ($fileSize > $rule['max_size']) {
            $maxSizeMB = $rule['max_size'] / (1024 * 1024);
            throw new MyException("文件大小超过限制，最大允许 {$maxSizeMB}MB", 422);
        }

        // 验证文件不能为空
        if ($fileSize === 0) {
            throw new MyException('上传的文件为空', 422);
        }

        // 验证文件扩展名
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $rule['mimes'])) {
            $allowedTypes = implode(', ', $rule['mimes']);
            throw new MyException("不支持的文件格式，允许的格式: {$allowedTypes}", 422);
        }

        // 验证MIME类型
        /* try {
            $mimeType = $file->getMimeType();
        } catch (\Exception $e) {
            throw new MyException('无法获取文件MIME类型：' . $e->getMessage(), 422);
        }

        $allowedMimes = [
            'image' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
            'video' => ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv'],
            'pdf' => ['application/pdf']
        ]; */

        /* if (isset($allowedMimes[$fileType]) && !in_array($mimeType, $allowedMimes[$fileType])) {
            throw new MyException('文件MIME类型不匹配'.$mimeType, 422);
        } */

        // 验证文件名安全性
        $this->validateFileName($file->getClientOriginalName());
    }

    /**
     * 验证原始URL是否属于该商品的待处理文件
     *
     * @param GoodsModel $goods
     * @param string $originalUrl
     * @param string $fieldName
     * @throws MyException
     */
    private function validateOriginalUrl(GoodsModel $goods, string $originalUrl, string $fieldName): void
    {
        $mediaFiles = $this->mediaInfoService->getPendingMediaFiles($goods);
        $allFiles = array_merge($mediaFiles['images'], $mediaFiles['videos'], $mediaFiles['pdfs']);

        $found = false;
        foreach ($allFiles as $file) {
            if ($file['url'] === $originalUrl && $file['field'] === $fieldName) {
                $found = true;
                break;
            }
        }

        if (!$found) {
            throw new MyException('原始URL不属于该商品的待处理文件', 422);
        }
    }

    /**
     * 存储上传的文件
     * 基于ImageProcessService的存储逻辑
     *
     * @param UploadedFile $file
     * @param int $userId
     * @param int $goodsId
     * @param string $fileType
     * @param string $fieldType
     * @param string $originalUrl
     * @return string|null
     */
    private function storeUploadedFile(UploadedFile $file, int $userId, int $goodsId, string $fileType, string $fieldType, string $originalUrl): ?string
    {
        try {
            // 生成文件名
            $fileName = $this->generateFileName($originalUrl, $file->getClientOriginalExtension());

            // 确定子目录
            $subDir = $this->getFileSubDirectory($fieldType);

            // 生成存储路径
            $relativePath = $this->generateFilePath($userId, $goodsId, $subDir, $fileName);
            $relativePath = $this->normalizePath($relativePath);
            $fullPath = public_path($relativePath);

            // 检查文件是否已存在
            if (file_exists($fullPath)) {
                return $relativePath;
            }

            // 创建目录
            $dir = dirname($fullPath);
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new \Exception("无法创建目录: {$dir}");
                }
            }

            // 移动文件到目标位置
            if ($file->move(dirname($fullPath), basename($fullPath))) {
                return $relativePath;
            }

            return null;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 验证字段更新数据
     *
     * @param array $fieldUpdates
     * @throws MyException
     */
    private function validateFieldUpdates(array $fieldUpdates): void
    {
        $allowedFields = [
            'goods_pic', 'goods_detail', 'goods_video', 'goods_pdf',
            'instruction_images', 'sku_thumb', 'sku_skc_gallery'
        ];

        foreach ($fieldUpdates as $field => $value) {
            if (!in_array($field, $allowedFields)) {
                throw new MyException("不允许更新字段: {$field}", 422);
            }

            // 验证字段值格式
            if (in_array($field, ['goods_pic', 'goods_detail', 'instruction_images', 'sku_thumb', 'sku_skc_gallery'])) {
                // 数组字段验证
                if (!is_array($value)) {
                    throw new MyException("字段 {$field} 应该是数组格式", 422);
                }

                // 验证数组元素格式
                $this->validateArrayFieldItems($field, $value);

            } elseif (in_array($field, ['goods_video', 'goods_pdf'])) {
                // 视频和PDF字段验证
                if (!is_array($value) || !isset($value['url']) || !isset($value['field'])) {
                    throw new MyException("字段 {$field} 应该包含 url 和 field 属性", 422);
                }

                // 验证URL格式
                if (!is_string($value['url']) || empty($value['url'])) {
                    throw new MyException("字段 {$field} 的 url 不能为空", 422);
                }

                // 验证field值
                if ($value['field'] !== $field) {
                    throw new MyException("字段 {$field} 的 field 属性值不匹配", 422);
                }
            }
        }
    }

    /**
     * 验证数组字段的元素格式
     *
     * @param string $field
     * @param array $items
     * @throws MyException
     */
    private function validateArrayFieldItems(string $field, array $items): void
    {
        foreach ($items as $index => $item) {
            if (!is_array($item)) {
                throw new MyException("字段 {$field} 的第 {$index} 个元素应该是数组格式", 422);
            }

            // 验证必需的字段
            if (!isset($item['url']) || !isset($item['field'])) {
                throw new MyException("字段 {$field} 的第 {$index} 个元素缺少必需的 url 或 field 属性", 422);
            }

            // 验证URL
            if (!is_string($item['url']) || empty($item['url'])) {
                throw new MyException("字段 {$field} 的第 {$index} 个元素的 url 不能为空", 422);
            }

            // 验证field值
            $expectedField = $this->getExpectedFieldValue($field);
            if ($item['field'] !== $expectedField) {
                throw new MyException("字段 {$field} 的第 {$index} 个元素的 field 属性值应该是 {$expectedField}", 422);
            }

            // 特殊字段的额外验证
            if ($field === 'instruction_images') {
                if (!isset($item['model_id'])) {
                    throw new MyException("字段 instruction_images 的第 {$index} 个元素缺少 model_id 属性", 422);
                }
                if (!is_numeric($item['model_id']) || $item['model_id'] <= 0) {
                    throw new MyException("字段 instruction_images 的第 {$index} 个元素的 model_id 必须是正整数", 422);
                }
            }

            if (in_array($field, ['sku_thumb', 'sku_skc_gallery'])) {
                if (!isset($item['sku_id'])) {
                    throw new MyException("字段 {$field} 的第 {$index} 个元素缺少 sku_id 属性", 422);
                }
                if (!is_numeric($item['sku_id']) || $item['sku_id'] <= 0) {
                    throw new MyException("字段 {$field} 的第 {$index} 个元素的 sku_id 必须是正整数", 422);
                }
            }

            // 验证URL格式（检查是否为本地路径）
            if (!$this->isValidLocalUrl($item['url'])) {
                throw new MyException("字段 {$field} 的第 {$index} 个元素的 url 格式无效", 422);
            }
        }
    }

    /**
     * 获取字段期望的field值
     *
     * @param string $field
     * @return string
     */
    private function getExpectedFieldValue(string $field): string
    {
        $fieldMapping = [
            'goods_pic' => 'goods_pic',
            'goods_detail' => 'goods_detail',
            'instruction_images' => 'urls',
            'sku_thumb' => 'thumb_url',
            'sku_skc_gallery' => 'skc_gallery'
        ];

        return $fieldMapping[$field] ?? $field;
    }

    /**
     * 验证是否为有效的本地URL
     *
     * @param string $url
     * @return bool
     */
    private function isValidLocalUrl(string $url): bool
    {
        // 检查是否以 attachment/ 开头
        if (strpos($url, 'attachment/') !== 0) {
            return false;
        }

        // 检查是否包含危险字符
        $dangerousChars = ['..', '<', '>', '"', "'", '&', '?', '#'];
        foreach ($dangerousChars as $char) {
            if (strpos($url, $char) !== false) {
                return false;
            }
        }

        return true;
    }

    /**
     * 更新商品字段
     * 基于ImageProcessService的字段更新逻辑
     *
     * @param GoodsModel $goods
     * @param array $fieldUpdates
     * @return array
     */
    private function updateGoodsFields(GoodsModel $goods, array $fieldUpdates): array
    {
        $updatedFields = [];

        foreach ($fieldUpdates as $field => $value) {
            switch ($field) {
                case 'goods_pic':
                case 'goods_detail':
                    // 商品图片字段：更新JSON数组中的URL
                    $updatedFields[$field] = $this->updateImageField($goods, $field, $value);
                    break;

                case 'instruction_images':
                    // 说明图片字段：更新GoodsInstructionImagesModel表
                    $updatedFields[$field] = $this->updateInstructionImagesField($goods, $value);
                    break;

                case 'goods_video':
                case 'goods_pdf':
                    // 视频和PDF字段：直接更新字符串值
                    $goods->$field = $value['url'];
                    $goods->save();
                    $updatedFields[$field] = $value['url'];
                    break;

                case 'sku_thumb':
                    // SKU缩略图：按sku_id分组更新
                    $updatedFields[$field] = $this->updateSkuField($goods, $field, $value);
                    break;

                case 'sku_skc_gallery':
                    // SKU图库：按sku_id分组更新
                    $updatedFields[$field] = $this->updateSkuGalleryField($goods, $field, $value);
                    break;
            }
        }

        return $updatedFields;
    }

    /**
     * 生成文件存储路径
     * 根据用户ID阈值决定使用新旧目录结构
     * 基于ImageProcessService::generateFilePath的逻辑
     *
     * @param int $userId 用户ID
     * @param int $goodsId 商品ID
     * @param string $subDir 子目录名称
     * @param string $fileName 文件名
     * @return string 相对路径
     */
    private function generateFilePath(int $userId, int $goodsId, string $subDir, string $fileName): string
    {
        if ($userId <= self::MIN_USER_ID_THRESHOLD) {
            // 使用旧目录结构：会员ID/商品ID/子目录/文件
            return "attachment/{$userId}/{$goodsId}/{$subDir}/{$fileName}";
        } else {
            // 使用新目录结构：商品ID/子目录/文件
            return "attachment/{$goodsId}/{$subDir}/{$fileName}";
        }
    }

    /**
     * 根据文件类型获取子目录名称
     *
     * @param string $fieldType
     * @return string
     */
    private function getFileSubDirectory(string $fieldType): string
    {
        switch ($fieldType) {
            case 'goods_pic':
            case 'goods_detail':
                // goods_detail 图片存储在与 goods_pic 相同的目录中
                return 'goods_pic';
            case 'instruction_images':
                return 'goods_instruction_images';
            case 'sku_thumb':
            case 'sku_skc_gallery':
                return 'sku';
            case 'goods_video':
                return 'goods_video';
            case 'goods_pdf':
                return 'goods_pdf';
            default:
                return 'other';
        }
    }

    /**
     * 从URL生成文件名
     *
     * @param string $url
     * @param string $extension
     * @return string
     */
    private function generateFileName(string $url, string $extension): string
    {
        // 解析URL获取文件名
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['path'])) {
            $pathInfo = pathinfo($parsedUrl['path']);
            if (isset($pathInfo['filename']) && !empty($pathInfo['filename'])) {
                return $pathInfo['filename'] . '.' . strtolower($extension);
            }
        }

        // 如果无法从URL提取文件名，使用时间戳和随机数生成
        return time() . '_' . mt_rand(1000, 9999) . '.' . strtolower($extension);
    }

    /**
     * 更新图片字段（JSON数组格式）-- 不需要操作已经本地化的数据 逻辑已经调整 会传递完整的数据过来
     *
     * 适用于 goods_pic 和 goods_detail 字段
     *
     * @param GoodsModel $goods
     * @param string $field
     * @param array $newData
     * @return array
     */
    private function updateImageField(GoodsModel $goods, string $field, array $newData): array
    {
        // 获取当前字段值
        /* $currentData = [];
        if (!empty($goods->$field)) {
            $currentData = json_decode($goods->$field, true);
            if (!is_array($currentData)) {
                $currentData = [];
            }
        } */

        // 保留已经本地化的URL
        $allData = [];
        /* foreach ($currentData as $item) {
            if (is_string($item) && !$this->isRemoteUrl($item)) {
                $allData[] = $item;
            } elseif (is_array($item) && isset($item['url']) && !$this->isRemoteUrl($item['url'])) {
                $allData[] = $item['url'];
            }
        } */

        // 添加新的本地URL
        foreach ($newData as $item) {
            if (isset($item['url']) && !empty($item['url'])) {
                $allData[] = $item['url'];
            }
        }

        // 更新字段
        if (empty($allData)) {
            $goods->$field = '';
        } else {
            $goods->$field = json_encode($allData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        $goods->save();

        return $allData;
    }

    /**
     * 更新说明图片字段 -- 不需要操作已经本地化的数据 逻辑已经调整 会传递完整的数据过来
     * 更新 GoodsInstructionImagesModel 表
     *
     * @param GoodsModel $goods
     * @param array $newData
     * @return array
     */
    private function updateInstructionImagesField(GoodsModel $goods, array $newData): array
    {
        $updatedData = [];

        // 按 model_id 分组处理
        $groupedData = [];
        foreach ($newData as $item) {
            $modelId = $item['model_id'];
            if (!isset($groupedData[$modelId])) {
                $groupedData[$modelId] = [];
            }
            $groupedData[$modelId][] = $item['url'];
        }

        foreach ($groupedData as $modelId => $urls) {
            // 查找对应的说明图片记录
            $instructionModel = GoodsInstructionImagesModel::where('id', $modelId)
                ->where('user_goods_id', $goods->id)
                ->first();

            if (!$instructionModel) {
                continue;
            }
 
            // 获取当前URLs
            /*$currentUrls = [];
               if (!empty($instructionModel->urls)) {
                $currentUrls = json_decode($instructionModel->urls, true);
                if (!is_array($currentUrls)) {
                    $currentUrls = [];
                }
            } */

            // 保留已经本地化的URL
            $allUrls = [];
            /* foreach ($currentUrls as $url) {
                if (is_string($url) && !$this->isRemoteUrl($url)) {
                    $allUrls[] = $url;
                }
            } */

            // 添加新的本地URL
            foreach ($urls as $url) {
                if (!empty($url)) {
                    $allUrls[] = $url;
                }
            }

            // 更新字段
            if (empty($allUrls)) {
                $instructionModel->urls = '';
            } else {
                $instructionModel->urls = json_encode($allUrls, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }

            $instructionModel->save();

            $updatedData[] = [
                'model_id' => $modelId,
                'urls' => $allUrls
            ];
        }

        return $updatedData;
    }

    /**
     * 更新SKU缩略图字段 -- 不需要操作已经本地化的数据 逻辑已经调整 会传递完整的数据过来
     *
     * @param GoodsModel $goods
     * @param string $field
     * @param array $skuData
     * @return array
     */
    private function updateSkuField(GoodsModel $goods, string $field, array $skuData): array
    {
        $updatedData = [];

        foreach ($skuData as $item) {
            if (!isset($item['sku_id']) || !isset($item['url'])) {
                continue;
            }

            $skuId = (int)$item['sku_id'];
            $url = $item['url'];

            // 查找对应的SKU记录，确保属于当前商品
            $sku = GoodsSkuModel::where('id', $skuId)
                ->where('user_goods_id', $goods->id)
                ->first();

            if (!$sku) {
                continue;
            }

            // 更新SKU缩略图
            $sku->thumb_url = $url;
            $sku->save();

            $updatedData[] = [
                'sku_id' => $skuId,
                'url' => $url,
                'updated_at' => $sku->updated_at
            ];
        }

        return $updatedData;
    }

    /**
     * 更新SKU图库字段 -- 不需要操作已经本地化的数据 逻辑已经调整 会传递完整的数据过来
     *
     * @param GoodsModel $goods
     * @param string $field
     * @param array $skuGalleryData
     * @return array
     */
    private function updateSkuGalleryField(GoodsModel $goods, string $field, array $skuGalleryData): array
    {
        $updatedData = [];

        // 按 sku_id 分组处理
        $groupedData = [];
        foreach ($skuGalleryData as $item) {
            if (!isset($item['sku_id']) || !isset($item['url'])) {
                continue;
            }

            $skuId = (int)$item['sku_id'];
            if (!isset($groupedData[$skuId])) {
                $groupedData[$skuId] = [];
            }
            $groupedData[$skuId][] = $item['url'];
        }

        foreach ($groupedData as $skuId => $urls) {
            // 查找对应的SKU记录，确保属于当前商品
            $sku = GoodsSkuModel::where('id', $skuId)
                ->where('user_goods_id', $goods->id)
                ->first();

            if (!$sku) {
                continue;
            }

            // 获取当前图库值
            /* $currentGallery = [];
            if (!empty($sku->skc_gallery)) {
                $currentGallery = json_decode($sku->skc_gallery, true);
                if (!is_array($currentGallery)) {
                    $currentGallery = [];
                }
            } */

            // 保留已经本地化的URL
            $allGalleryUrls = [];
            /* foreach ($currentGallery as $galleryUrl) {
                if (is_string($galleryUrl) && !$this->isRemoteUrl($galleryUrl)) {
                    $allGalleryUrls[] = $galleryUrl;
                }
            } */

            // 添加新的本地URL
            foreach ($urls as $url) {
                if (!empty($url) && !in_array($url, $allGalleryUrls)) {
                    $allGalleryUrls[] = $url;
                }
            }

            // 更新SKU图库
            if (empty($allGalleryUrls)) {
                $sku->skc_gallery = '';
            } else {
                $sku->skc_gallery = json_encode($allGalleryUrls, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
            $sku->save();

            $updatedData[] = [
                'sku_id' => $skuId,
                'urls' => $urls,
                'gallery' => $allGalleryUrls,
                'updated_at' => $sku->updated_at
            ];
        }

        return $updatedData;
    }

    /**
     * 判断URL是否为远程URL
     *
     * @param string $url
     * @return bool
     */
    private function isRemoteUrl(string $url): bool
    {
        return (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) &&
               strpos($url, 'attachment/') !== 0;
    }

    /**
     * 验证文件名安全性
     *
     * @param string $fileName
     * @throws MyException
     */
    private function validateFileName(string $fileName): void
    {
        // 检查文件名长度
        if (strlen($fileName) > 1024) {
            throw new MyException('文件名过长', 1024);
        }
        // 检查危险字符
        $dangerousChars = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*', "\0"];
        foreach ($dangerousChars as $char) {
            if (strpos($fileName, $char) !== false) {
                throw new MyException('文件名包含非法字符', 422);
            }
        }
    }

    /**
     * 规范化文件路径，防止目录遍历攻击
     *
     * @param string $path
     * @return string
     */
    private function normalizePath(string $path): string
    {
        // 移除危险字符
        $path = str_replace(['../', '.\\', '..\\'], '', $path);

        // 规范化路径分隔符
        $path = str_replace('\\', '/', $path);

        // 移除多余的斜杠
        $path = preg_replace('/\/+/', '/', $path);

        // 确保路径不以斜杠开头（相对路径）
        $path = ltrim($path, '/');

        return $path;
    }

    /**
     * 设置商品图片本地化错误状态
     *
     * @param User $user
     * @param int $goodsId
     * @return array
     * @throws MyException
     */
    public function setImageLocalError(User $user, int $goodsId): array
    {
        // 查找商品
        $goods = GoodsModel::where('id', $goodsId)
            ->where('user_id', $user->id)
            ->first();

        if (!$goods) {
            throw new MyException('商品不存在或无权限访问', 404);
        }

        // 使用数据库事务确保数据一致性
        return DB::transaction(function () use ($goods, $goodsId) {
            try {
                // 设置图片本地化错误状态
                $goods->img_local_error = 1;
                $goods->updated_at = now();
                $goods->save();
                return [
                    'message' => '商品图片本地化错误状态设置成功',
                    'goods_id' => $goodsId,
                    'img_local_error' => $goods->img_local_error,
                    'updated_at' => $goods->updated_at
                ];

            } catch (\Exception $e) {
                throw new MyException('设置商品图片本地化错误状态失败', 500);
            }
        });
    }
}
