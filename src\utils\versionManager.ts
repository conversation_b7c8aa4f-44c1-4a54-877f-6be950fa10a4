/**
 * 版本管理器
 * 用于管理Chrome扩展的版本号获取
 */

interface Version {
  major: number;    // 主版本号
  minor: number;    // 次版本号
  patch: number;    // 修订号
  raw: string;      // 原始版本字符串 (e.g., "2.1.3")
}



export class VersionManager {
  private static instance: VersionManager;

  private constructor() {}

  /**
   * 获取版本管理器单例实例
   */
  public static getInstance(): VersionManager {
    if (!VersionManager.instance) {
      VersionManager.instance = new VersionManager();
    }
    return VersionManager.instance;
  }

  /**
   * 从manifest.json读取当前版本号
   * @returns Promise<string> 版本号字符串
   */
  public async getCurrentVersion(): Promise<string> {
    try {
      // 在Chrome扩展环境中，可以通过chrome.runtime.getManifest()获取manifest信息
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
        const manifest = chrome.runtime.getManifest();
        return manifest.version || '1.0.0';
      }

      // 如果不在Chrome扩展环境中，返回默认版本
      console.warn('无法获取manifest信息，使用默认版本');
      return '1.0.0';
    } catch (error) {
      console.error('获取版本号失败:', error);
      return '1.0.0';
    }
  }

  /**
   * 解析版本号字符串为Version对象
   * @param versionString 版本号字符串 (e.g., "2.1.3")
   * @returns Version 解析后的版本对象
   */
  public parseVersion(versionString: string): Version {
    const parts = versionString.split('.');

    if (parts.length !== 3) {
      throw new Error(`无效的版本号格式: ${versionString}，应为 x.y.z 格式`);
    }

    const major = parseInt(parts[0], 10);
    const minor = parseInt(parts[1], 10);
    const patch = parseInt(parts[2], 10);

    if (isNaN(major) || isNaN(minor) || isNaN(patch)) {
      throw new Error(`版本号包含非数字字符: ${versionString}`);
    }

    return {
      major,
      minor,
      patch,
      raw: versionString
    };
  }

  /**
   * 验证版本号格式是否正确
   * @param version 版本号字符串
   * @returns boolean 是否为有效格式
   */
  public isValidVersionFormat(version: string): boolean {
    try {
      this.parseVersion(version);
      return true;
    } catch {
      return false;
    }
  }



  /**
   * 获取版本号（实时从manifest获取，不缓存）
   * @returns Promise<string> 版本号字符串
   */
  public async getVersion(): Promise<string> {
    try {
      // 实时从manifest获取版本号
      return await this.getCurrentVersion();
    } catch (error) {
      console.error('获取版本号失败:', error);
      return '1.0.0';
    }
  }


}

// 导出单例实例
export const versionManager = VersionManager.getInstance();

// 导出类型定义
export type { Version };
