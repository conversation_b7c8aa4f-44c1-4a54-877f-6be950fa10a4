interface FetchConfig {
  url?: string;
  method?: string;
  options?: RequestInit;
}

interface FetchApi {
  [key: string]: (
    body: any,
    callback: (response: any) => any,
    headers?: HeadersInit,
    config?: FetchConfig
  ) => void;
}

export default function putFetch(
  api: FetchApi,
  name: string,
  initialUrl: string,
  { initialMethod = 'get' } = {}
) {
  api[name] = (body: any, callback: (arg0: any) => any, headers = {}, config: FetchConfig = {}) => {
    const finalUrl = config.url || initialUrl;
    const finalMethod = config.method || initialMethod;
    const { url: _, method: __, ...restOptions } = config;

    // 构建fetch选项
    const fetchOptions: RequestInit = {
      method: finalMethod,
      headers: new Headers({
        ...headers
      }),
      ...restOptions
    };

    // 处理请求体
    if (finalMethod.toLowerCase() !== 'get' && body) {
      if (headers['content-type']?.includes('application/json')) {
        fetchOptions.body = JSON.stringify(body);
      } else {
        fetchOptions.body = body;
      }
    }

    // 构建URL(get请求添加查询参数)
    const finalUrlWithParams = finalMethod.toLowerCase() === 'get' && body
      ? `${finalUrl}?${new URLSearchParams(body)}`
      : finalUrl;

    // 执行fetch请求
    fetch(finalUrlWithParams, fetchOptions)
      .then(async response => {
        try {
          const data = await response.json();
          callback([{
            status: response.status,
            data,
            headers: Object.fromEntries(response.headers)
          }]);
        } catch (error) {
          console.error(`Error in ${name} fetch call:`, error);
          callback(null);
        }
      })
      .catch(error => {
        console.error(`Network error in ${name} fetch call:`, error);
        callback(null);
      });
  };
}
