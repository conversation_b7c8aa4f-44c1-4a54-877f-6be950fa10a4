<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 版本验证配置
    |--------------------------------------------------------------------------
    |
    | 这里配置版本验证相关的参数，包括支持的版本范围、错误消息等
    |
    */

    // 是否启用版本验证
    'validation_enabled' => env('VERSION_VALIDATION_ENABLED', true),

    // 最小支持版本
    'min_supported_version' => env('MIN_SUPPORTED_VERSION', '2.1.3'),

    // 最大支持版本
    'max_supported_version' => env('MAX_SUPPORTED_VERSION', '2.3.5'),

    // 当前最新版本
    'current_latest_version' => env('CURRENT_LATEST_VERSION', '2.3.5'),

    // 错误消息配置
    'messages' => [
        'version_too_low' => env('VERSION_TOO_LOW_MESSAGE', '您需要升级到最新版本才能使用,请联系客服升级最新版本'),
        'version_too_high' => env('VERSION_TOO_HIGH_MESSAGE', '您需要升级到最新版本才能使用,请联系客服获取最新版本'),
        'version_invalid_format' => env('VERSION_INVALID_FORMAT_MESSAGE', '您需要升级到最新版本才能使用,请联系客服升级最新版本'),
        'version_missing' => env('VERSION_MISSING_MESSAGE', '您需要升级到最新版本才能使用,请联系客服升级最新版本'),
        'upgrade_instruction' => env('UPGRADE_INSTRUCTION_MESSAGE', '请联系客服升级到最新版本'),
    ],

    // 升级提示消息（向后兼容）
    'upgrade_message' => env('VERSION_TOO_LOW_MESSAGE', '您需要升级到最新版本才能使用,请联系客服升级最新版本'),
];