<template>
  <div class="version-upgrade-container">
    <div class="version-upgrade-card">
      <div class="icon-container">
        <div class="upgrade-icon">🔄</div>
      </div>

      <h2 class="title">版本升级提示</h2>

      <p class="message">
        {{ errorMessage || '您当前的扩展版本需要升级才能继续使用' }}
      </p>

      <div class="version-info">
        <div class="version-item" v-if="currentVersion">
          <span class="version-label">当前版本：</span>
          <span class="version-value current">{{ currentVersion }}</span>
        </div>
        <div class="version-item" v-if="latestVersion">
          <span class="version-label">最新版本：</span>
          <span class="version-value latest">{{ latestVersion }}</span>
        </div>
      </div>

      <div class="instructions">
        <h3>升级指引：</h3>
        <div v-if="errorCode === '2001'" class="upgrade-steps">
          <ol>
            <li>请联系客服获取最新版本的扩展安装包</li>
            <li>卸载当前版本的扩展程序</li>
            <li>安装新版本的扩展程序</li>
            <li>重新登录您的账户</li>
          </ol>
        </div>
        <div v-else-if="errorCode === '2002'" class="downgrade-steps">
          <ol>
            <li>您的扩展版本过高，可能存在兼容性问题</li>
            <li>请联系客服获取兼容的扩展版本</li>
            <li>或等待系统升级以支持您的扩展版本</li>
          </ol>
        </div>
        <div v-else-if="errorCode === '2003'" class="format-error-steps">
          <ol>
            <li>扩展版本号格式异常</li>
            <li>请重新安装扩展程序</li>
            <li>如问题持续存在，请联系技术支持</li>
          </ol>
        </div>
        <div v-else-if="errorCode === '2004'" class="missing-version-steps">
          <ol>
            <li>扩展版本信息缺失</li>
            <li>请更新到最新版本的扩展程序</li>
            <li>确保扩展程序完整安装</li>
          </ol>
        </div>
        <div v-else class="general-steps">
          <ol>
            <li>请联系客服获取最新版本的扩展安装包</li>
            <li>按照客服指引进行升级操作</li>
            <li>升级完成后重新登录使用</li>
          </ol>
        </div>
      </div>
      <div class="footer">
        <p class="footer-text">
          感谢您的理解与配合，我们将持续优化产品体验
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'


const route = useRoute()
const router = useRouter()

// 版本信息
const currentVersion = ref('')
const minVersion = ref('')
const maxVersion = ref('')
const latestVersion = ref('')
const errorCode = ref('')
const errorMessage = ref('')

// 从路由参数中获取版本信息
onMounted(() => {
  const query = route.query
  currentVersion.value = (query.currentVersion as string) || ''
  minVersion.value = (query.minVersion as string) || ''
  maxVersion.value = (query.maxVersion as string) || ''
  latestVersion.value = (query.latestVersion as string) || ''
  errorCode.value = (query.errorCode as string) || ''
  errorMessage.value = (query.errorMessage as string) || ''

  console.log('版本升级页面参数:', {
    currentVersion: currentVersion.value,
    minVersion: minVersion.value,
    maxVersion: maxVersion.value,
    latestVersion: latestVersion.value,
    errorCode: errorCode.value,
    errorMessage: errorMessage.value
  })
})


</script>

<style scoped>
.version-upgrade-container {
  display: flex;
  justify-content: center;
  background: linear-gradient(135deg, #8e9eab 0%, #616161 100%);
  padding: 20px;
}

.version-upgrade-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.icon-container {
  display: none;
  margin-bottom: 24px;
}

.upgrade-icon {
  font-size: 4rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.title {
  color: #333;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
  margin-top: 0;
}

.message {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 32px;
}

.version-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
  text-align: left;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-label {
  color: #555;
  font-weight: 500;
}

.version-value {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.version-value.current {
  background: #fff3cd;
  color: #856404;
}

.version-value.latest {
  background: #d4edda;
  color: #155724;
}

.version-value.required {
  background: #f8d7da;
  color: #721c24;
}

.version-value.max {
  background: #d1ecf1;
  color: #0c5460;
}

.instructions {
  text-align: left;
  background: #e3f2fd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.instructions h3 {
  color: #333;
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 12px;
}

.instructions ol {
  color: #555;
  line-height: 1.6;
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
}



.footer {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.footer-text {
  color: #999;
  font-size: 0.9rem;
  margin: 0;
}

@media (max-width: 768px) {
  .version-upgrade-card {
    padding: 24px;
    margin: 10px;
  }
}
</style>
