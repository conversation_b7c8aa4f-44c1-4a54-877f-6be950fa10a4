#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置设置
"""

from typing import Dict, Any


class AppSettings:
    """应用程序设置类"""
    
    # 应用程序信息
    APP_NAME = "跨境蜂助手"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "跨境蜂"
    
    # 数据库配置
    DATABASE_NAME = "kjf.db"
    
    # API配置（根据运行环境动态设置）
    @classmethod
    def get_api_base_url(cls) -> str:
        """获取API基础URL"""
        from utils.helpers import is_development_environment
        if is_development_environment():
            #return "https://trade.dailyhotnews.com.cn/api" 
            return "http://tsa.test.com/api"  # 开发环境URL
        else:
            return "https://trade.dailyhotnews.com.cn/api"  # 打包环境URL
    API_TIMEOUT = 30
    
    # UI配置
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    WINDOW_MIN_WIDTH = 600
    WINDOW_MIN_HEIGHT = 400
    
    # 日志配置
    LOG_FORMAT = "[{timestamp}] [{level}] {message}"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

    # 日志输出模式
    LOG_OUTPUT_GUI = "gui"          # 输出到GUI界面
    LOG_OUTPUT_FILE = "file"        # 输出到文件
    LOG_OUTPUT_SILENT = "silent"    # 静默模式（不输出）

    # 默认日志输出模式（根据运行环境动态设置）
    @classmethod
    def get_default_log_output(cls) -> str:
        """获取默认日志输出模式"""
        from utils.helpers import is_development_environment
        if is_development_environment():
            return cls.LOG_OUTPUT_FILE  # 开发环境输出到文件
        else:
            return cls.LOG_OUTPUT_SILENT  # 打包环境静默模式

    # 日志文件配置
    LOG_FILE_NAME = "kjf.log"
    
    @classmethod
    def get_database_path(cls) -> str:
        """获取数据库文件路径"""
        # 使用统一的路径管理工具
        from utils.helpers import get_database_path
        return get_database_path(cls.DATABASE_NAME)

    @classmethod
    def get_log_file_path(cls) -> str:
        """获取日志文件路径"""
        from utils.helpers import get_log_directory
        import os
        log_dir = get_log_directory()
        return os.path.join(log_dir, cls.LOG_FILE_NAME)
    
    @classmethod
    def get_app_info(cls) -> Dict[str, str]:
        """获取应用程序信息"""
        return {
            'name': cls.APP_NAME,
            'version': cls.APP_VERSION,
            'author': cls.APP_AUTHOR
        }
