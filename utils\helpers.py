#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数集合
"""

import os
import sys
import shutil
import sqlite3
from typing import Optional, Dict, Any, Tuple
from datetime import datetime


def is_development_environment() -> bool:
    """
    检测当前是否为开发环境

    Returns:
        True: 开发环境
        False: 打包后的EXE环境
    """
    return not getattr(sys, 'frozen', False)


def get_app_directory() -> str:
    """
    获取应用程序目录

    对于打包后的EXE文件，返回EXE文件所在的目录
    对于开发环境，返回项目根目录

    Returns:
        应用程序目录路径
    """
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的环境
        # sys.executable 指向EXE文件的完整路径
        return os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


def get_resource_directory() -> str:
    """
    获取资源文件目录

    Returns:
        资源文件目录路径
    """
    app_dir = get_app_directory()
    return os.path.join(app_dir, "resource")


def get_icon_path(icon_name: str = "app_icon.ico") -> Optional[str]:
    """
    获取图标文件路径，支持开发环境和打包环境

    Args:
        icon_name: 图标文件名

    Returns:
        图标文件路径，如果不存在返回None
    """
    # 方法1：尝试从应用程序目录加载
    app_dir = get_app_directory()
    icon_path = os.path.join(app_dir, "resource", "icons", icon_name)

    if os.path.exists(icon_path):
        return icon_path

    # 方法2：如果是打包环境，尝试从PyInstaller临时目录加载
    if getattr(sys, 'frozen', False):
        try:
            # PyInstaller会将数据文件解压到临时目录
            if hasattr(sys, '_MEIPASS'):
                temp_icon_path = os.path.join(sys._MEIPASS, "resource", "icons", icon_name)
                if os.path.exists(temp_icon_path):
                    return temp_icon_path
        except Exception:
            pass

    # 方法3：尝试相对路径（开发环境备用）
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        relative_icon_path = os.path.join(project_root, "resource", "icons", icon_name)
        if os.path.exists(relative_icon_path):
            return relative_icon_path
    except Exception:
        pass

    return None


def get_database_directory() -> str:
    """
    获取数据库文件目录

    Returns:
        数据库文件目录路径
    """
    resource_dir = get_resource_directory()
    db_dir = os.path.join(resource_dir, "db")

    # 确保目录存在
    ensure_directory_exists(db_dir)

    return db_dir


def get_database_path(db_name: str = "kjf.db") -> str:
    """
    获取数据库文件完整路径

    Args:
        db_name: 数据库文件名

    Returns:
        数据库文件完整路径
    """
    db_dir = get_database_directory()
    return os.path.join(db_dir, db_name)


def get_log_directory() -> str:
    """
    获取日志文件目录

    Returns:
        日志文件目录路径
    """
    app_dir = get_app_directory()
    log_dir = os.path.join(app_dir, "log")

    # 确保目录存在
    ensure_directory_exists(log_dir)

    return log_dir


def get_log_file_path(log_name: str = "kjf.log") -> str:
    """
    获取日志文件完整路径

    Args:
        log_name: 日志文件名

    Returns:
        日志文件完整路径
    """
    log_dir = get_log_directory()
    return os.path.join(log_dir, log_name)


def format_timestamp(dt: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化时间戳
    
    Args:
        dt: 时间对象，如果为None则使用当前时间
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)


def mask_secret(secret: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    掩码处理敏感信息
    
    Args:
        secret: 需要掩码的字符串
        mask_char: 掩码字符
        visible_chars: 可见字符数量
        
    Returns:
        掩码后的字符串
    """
    if not secret:
        return ""
    
    if len(secret) <= visible_chars:
        return mask_char * len(secret)
    
    visible_part = secret[:visible_chars]
    masked_part = mask_char * (len(secret) - visible_chars)
    return visible_part + masked_part


def validate_input(value: str, field_name: str, required: bool = True,
                  min_length: int = 0, max_length: int = None) -> Tuple[bool, str]:
    """
    验证输入值
    
    Args:
        value: 输入值
        field_name: 字段名称
        required: 是否必填
        min_length: 最小长度
        max_length: 最大长度
        
    Returns:
        (是否有效, 错误消息)
    """
    if required and not value.strip():
        return False, f"{field_name}不能为空"
    
    if value and len(value) < min_length:
        return False, f"{field_name}长度不能少于{min_length}个字符"
    
    if value and max_length and len(value) > max_length:
        return False, f"{field_name}长度不能超过{max_length}个字符"
    
    return True, ""


def safe_get_dict_value(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        data: 字典数据
        key: 键名
        default: 默认值
        
    Returns:
        字典值或默认值
    """
    try:
        return data.get(key, default)
    except (AttributeError, TypeError):
        return default


def ensure_directory_exists(directory_path: str) -> bool:
    """
    确保目录存在，如果不存在则创建

    Args:
        directory_path: 目录路径

    Returns:
        是否成功
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception:
        return False


def get_attachment_directory() -> str:
    """
    获取附件目录路径

    Returns:
        附件目录的绝对路径
    """
    app_dir = get_app_directory()
    return os.path.join(app_dir, 'attachment')


def cleanup_system() -> Tuple[bool, str]:
    """
    执行系统清理操作
    包括：
    1. 清空attachment文件夹内容（保留文件夹本身）
    2. 清空数据库表记录并重置自增ID

    Returns:
        (是否成功, 错误消息)
    """
    try:
        # 1. 清理attachment文件夹
        attachment_dir = get_attachment_directory()
        cleanup_success, cleanup_error = cleanup_attachment_directory(attachment_dir)
        if not cleanup_success:
            return False, f"清理文件失败: {cleanup_error}"

        # 2. 清理数据库
        db_path = get_database_path()
        db_cleanup_success, db_cleanup_error = cleanup_database_tables(db_path)
        if not db_cleanup_success:
            return False, f"清理数据库失败: {db_cleanup_error}"

        return True, "系统清理完成"

    except Exception as e:
        return False, f"清理操作出错: {str(e)}"


def cleanup_attachment_directory(attachment_dir: str) -> Tuple[bool, str]:
    """
    清空attachment文件夹内容，保留文件夹本身

    Args:
        attachment_dir: attachment文件夹路径

    Returns:
        (是否成功, 错误消息)
    """
    try:
        if not os.path.exists(attachment_dir):
            # 如果文件夹不存在，创建它
            os.makedirs(attachment_dir, exist_ok=True)
            return True, "attachment文件夹不存在，已创建"

        # 删除文件夹内的所有内容
        for item in os.listdir(attachment_dir):
            item_path = os.path.join(attachment_dir, item)
            try:
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
            except Exception as e:
                # 如果某个文件删除失败，记录但继续删除其他文件
                print(f"删除文件/文件夹失败: {item_path}, 错误: {str(e)}")

        return True, "attachment文件夹清理完成"

    except Exception as e:
        return False, f"清理attachment文件夹出错: {str(e)}"


def cleanup_database_tables(db_path: str) -> Tuple[bool, str]:
    """
    清空数据库表记录并重置自增ID

    Args:
        db_path: 数据库文件路径

    Returns:
        (是否成功, 错误消息)
    """
    try:
        if not os.path.exists(db_path):
            return False, "数据库文件不存在"

        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 清空file_download_records表
            cursor.execute("DELETE FROM file_download_records")

            # 清空goods表
            cursor.execute("DELETE FROM goods")

            # 重置file_download_records表的自增ID
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='file_download_records'")

            # 重置goods表的自增ID
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='goods'")

            # 提交事务
            conn.commit()

            return True, "数据库表清理完成"

        except Exception as e:
            # 回滚事务
            conn.rollback()
            return False, f"数据库操作失败: {str(e)}"

        finally:
            # 关闭连接
            conn.close()

    except Exception as e:
        return False, f"连接数据库失败: {str(e)}"


def get_dpi_scale_factor(verbose: bool = False) -> float:
    """
    获取DPI缩放因子
    
    Args:
        verbose: 是否输出详细信息，默认为False
        
    Returns:
        DPI缩放因子，相对于96 DPI的倍数，范围限制在1.0-3.0之间
    """
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 获取应用程序实例
        app = QApplication.instance()
        if not app:
            return 1.0
        
        # 获取主显示器
        desktop = app.desktop()
        primary_screen = desktop.primaryScreen()
        screen = desktop.screen(primary_screen)
        
        # 计算DPI缩放因子（相对于96 DPI）
        dpi_x = screen.logicalDpiX()
        scale_factor = dpi_x / 96.0
        
        # 限制缩放因子在合理范围内
        scale_factor = max(1.0, min(scale_factor, 3.0))
        
        if verbose:
            print(f"✓ 检测到DPI: {dpi_x}, 缩放因子: {scale_factor:.2f}")
            
        return scale_factor
        
    except Exception as e:
        if verbose:
            print(f"⚠️  获取DPI缩放因子失败: {e}")
        return 1.0
