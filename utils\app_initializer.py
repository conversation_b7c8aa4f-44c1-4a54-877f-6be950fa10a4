#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序初始化模块
负责应用程序启动前的所有准备工作
"""

import os
import sys
from typing import Optional, Tuple
from PyQt5.QtWidgets import QApplication


class AppInitializer:
    """应用程序初始化器"""
    
    def __init__(self):
        """初始化应用程序初始化器"""
        self.app_info = {
            'name': '跨境蜂助手',
            'version': '1.0.0',
            'organization': '跨境蜂'
        }
    
    def setup_python_path(self) -> None:
        """设置Python路径"""
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
    
    def cleanup_log_files(self) -> None:
        """清理日志文件"""
        try:
            from config.settings import AppSettings
            log_file_path = AppSettings.get_log_file_path()
            if os.path.exists(log_file_path):
                os.remove(log_file_path)
                print(f"已清理日志文件: {log_file_path}")
        except Exception as e:
            print(f"清理日志文件时出错: {e}")
    
    def create_qt_application(self, argv: list) -> QApplication:
        """
        创建并配置QApplication实例
        
        Args:
            argv: 命令行参数
            
        Returns:
            配置好的QApplication实例
        """
        # 启用高DPI支持 - 必须在创建QApplication之前设置
        from PyQt5.QtCore import Qt
        try:
            # 启用高DPI自动缩放
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            # 使用高DPI像素图
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
            print("✓ 已启用高DPI支持")
        except Exception as e:
            print(f"⚠️  启用高DPI支持时出错: {e}")
        
        app = QApplication(argv)
        
        # 设置应用程序属性
        app.setApplicationName(self.app_info['name'])
        app.setApplicationVersion(self.app_info['version'])
        app.setOrganizationName(self.app_info['organization'])
        
        return app
    
    def perform_startup_checks(self) -> bool:
        """
        执行启动检查
        
        Returns:
            是否通过所有检查
        """
        try:
            # 检查必要的目录是否存在
            from utils.helpers import get_log_directory, get_database_directory, get_attachment_directory
            
            # 确保日志目录存在
            log_dir = get_log_directory()
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                print(f"创建日志目录: {log_dir}")
            
            # 确保数据库目录存在
            db_dir = get_database_directory()
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                print(f"创建数据库目录: {db_dir}")
            
            # 确保附件目录存在
            attachment_dir = get_attachment_directory()
            if not os.path.exists(attachment_dir):
                os.makedirs(attachment_dir, exist_ok=True)
                print(f"创建附件目录: {attachment_dir}")
            
            return True
            
        except Exception as e:
            print(f"启动检查失败: {e}")
            return False
    
    def initialize_application(self, argv: list) -> Tuple[Optional[QApplication], bool]:
        """
        完整的应用程序初始化流程
        
        Args:
            argv: 命令行参数
            
        Returns:
            (QApplication实例, 初始化是否成功)
        """
        try:
            # 1. 设置Python路径
            self.setup_python_path()
            
            # 2. 执行启动检查
            if not self.perform_startup_checks():
                return None, False
            
            # 3. 清理日志文件
            self.cleanup_log_files()
            
            # 4. 创建QApplication
            app = self.create_qt_application(argv)
            
            print("应用程序初始化完成")
            return app, True
            
        except Exception as e:
            print(f"应用程序初始化失败: {e}")
            return None, False


# 全局初始化器实例
app_initializer = AppInitializer()


def initialize_app(argv: list) -> Tuple[Optional[QApplication], bool]:
    """
    应用程序初始化的便捷函数
    
    Args:
        argv: 命令行参数
        
    Returns:
        (QApplication实例, 初始化是否成功)
    """
    return app_initializer.initialize_application(argv)


def cleanup_log_files() -> None:
    """
    清理日志文件的便捷函数
    """
    app_initializer.cleanup_log_files()
