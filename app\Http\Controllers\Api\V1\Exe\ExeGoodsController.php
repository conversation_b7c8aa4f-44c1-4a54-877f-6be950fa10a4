<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Exe;

use App\Models\User\User;
use Illuminate\Http\Request;
use App\Exceptions\MyException;
use Illuminate\Http\JsonResponse;
use App\Service\User\ExeGoodsService;
use App\Service\User\MediaInfoService;
use App\Http\Controllers\Api\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * EXE商品控制器
 * 为EXE程序提供专用的API接口
 */
class ExeGoodsController extends Controller
{
    protected MediaInfoService $mediaInfoService;
    protected ExeGoodsService $exeGoodsService;
    const MIN_USER_ID_THRESHOLD = 0;

    public function __construct(MediaInfoService $mediaInfoService, ExeGoodsService $exeGoodsService)
    {
        $this->mediaInfoService = $mediaInfoService;
        $this->exeGoodsService = $exeGoodsService;
        parent::__construct();
    }
    /**
     * 获取当前认证的API用户
     *
     * @param Request $request
     * @return User
     */
    protected function getApiUser(Request $request): User
    {
        $user = $request->attributes->get('api_user');
        if (!$user) {
            throw new MyException('用户认证信息缺失', 401);
        }
        return $user;
    }





    /**
     * 获取待处理商品信息
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPendingGoods(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $only_error = $request->input('only_error', 0);
            $only_error = (int)$only_error;
            if(!in_array($only_error,[0,1])){
                $only_error = 0;
            }
            // 调用服务类获取待处理商品数据
            $result = $this->exeGoodsService->getPendingGoodsData($user,$only_error);

            if (!$result) {
                return $this->exeSuccessResponse(['user_id' => $user->id,'phone'=>$user->phone], '暂无待处理商品');
            }
            // 清理响应数据
            $result = $this->sanitizeResponseData($result);

            return $this->exeSuccessResponse($result);

        } catch (MyException $e) {
            return $this->exeErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->exeErrorResponse('系统错误', 500);
        }
    }

    /**
     * 文件上传接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadFile(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $appId = $request->header('X-App-Id');
            // 调试：打印所有接收到的请求参数
            if(strtoupper($appId) === 'KJF9843289040422585'){
                // 安全地获取文件信息，避免多次访问临时文件
                $fileInfo = null;
                if ($request->file('file')) {
                    try {
                        $uploadedFile = $request->file('file');
                        $fileInfo = [
                            'original_name' => $uploadedFile->getClientOriginalName(),
                            'mime_type' => $uploadedFile->getMimeType(),
                            'size' => $uploadedFile->getSize(),
                            'is_valid' => $uploadedFile->isValid(),
                            'temp_path' => $uploadedFile->getPathname()
                        ];
                    } catch (\Exception $e) {
                        $fileInfo = ['error' => $e->getMessage()];
                    }
                }

                Log::info('文件上传请求调试信息', [
                    'all_params' => $request->all(),
                    'goods_id' => $request->input('goods_id'),
                    'goods_platform_id' => $request->input('goods_platform_id'),
                    'target_goods_id' => $request->input('goods_platform_id') ? (int)$request->input('goods_platform_id') : (int)$request->input('goods_id'),
                    'file_type_value' => $request->input('file_type'),
                    'file_type_raw' => $request->get('file_type'),
                    'file_info' => $fileInfo,
                    'content_type' => $request->header('Content-Type'),
                    'method' => $request->method(),
                    'user_id' => $user->id
                ]);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'goods_id' => 'required|integer|min:1',
                'goods_platform_id' => 'nullable|integer|min:1',  // 新增平台商品ID参数
                'file_type' => 'required|string|in:image,video,pdf',
                'field_type' => 'required|string|in:goods_pic,goods_detail,instruction_images,sku_thumb,goods_video,goods_pdf,sku_skc_gallery',
                'field_name' => 'required|string|in:goods_pic,goods_detail,urls,thumb_url,goods_video,goods_pdf,skc_gallery',
                'file' => 'required|file',
                'original_url' => 'required|string'
            ]);

            if ($validator->fails()) {
                if(strtoupper($appId) === 'KJF9843289040422585'){
                    Log::info('文件上传请求验证失败', [
                        'errors' => $validator->errors()
                    ]);
                }
                return $this->exeErrorResponse('参数验证失败', 422, $validator->errors());
            }

            $goodsId = (int)$request->input('goods_id');
            $goodsPlatformId = (int)$request->input('goods_platform_id');
            $fileType = $request->input('file_type');
            $fieldType = $request->input('field_type');
            $fieldName = $request->input('field_name');
            $originalUrl = $request->input('original_url');
            $uploadedFile = $request->file('file');

            // 调用服务类处理文件上传
            $result = $this->exeGoodsService->handleFileUpload(
                $user,
                $goodsId,
                $fileType,
                $fieldType,
                $fieldName,
                $uploadedFile,
                $originalUrl
            );
            return $this->exeSuccessResponse($this->sanitizeResponseData($result));

        } catch (MyException $e) {
            if(strtoupper($appId) === 'KJF9843289040422585'){
                Log::info('文件上传请求异常', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            return $this->exeErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            if(strtoupper($appId) === 'KJF9843289040422585'){
                Log::info('文件上传请求异常', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            return $this->exeErrorResponse('系统错误', 500);
        }
    }

    /**
     * 商品状态更新接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $appId = $request->header('X-App-Id');
            if(strtoupper($appId) === 'KJF9843289040422585'){
                Log::info('商品状态更新请求调试信息', [
                    $request->all()
                ]);
            }
            
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'goods_id' => 'required|integer|min:1',
                'field_updates' => 'required|array',
                'mark_completed' => 'boolean'
            ]);

            if ($validator->fails()) {
                return $this->exeErrorResponse('参数验证失败', 422, $validator->errors());
            }

            $goodsId = (int)$request->input('goods_id');
            $fieldUpdates = $request->input('field_updates', []);
            $markCompleted = $request->input('mark_completed', false);

            // 调用服务类更新商品状态
            $result = $this->exeGoodsService->updateGoodsStatus(
                $user,
                $goodsId,
                $fieldUpdates,
                $markCompleted
            );
            return $this->exeSuccessResponse($this->sanitizeResponseData($result));

        } catch (MyException $e) {
            return $this->exeErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->exeErrorResponse('系统错误', 500);
        }
    }

    /**
     * 设置商品图片本地化错误状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setImageLocalError(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'goods_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return $this->exeErrorResponse('参数验证失败', 422, $validator->errors());
            }

            $goodsId = (int)$request->input('goods_id');

            // 调用服务类设置错误状态
            $result = $this->exeGoodsService->setImageLocalError($user, $goodsId);

            return $this->exeSuccessResponse($this->sanitizeResponseData($result));

        } catch (MyException $e) {
            return $this->exeErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->exeErrorResponse('系统错误', 500);
        }
    }




}