<?php

declare(strict_types=1);

namespace App\Service\System;

use App\Utils\VersionComparator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

/**
 * 版本配置服务类
 * 
 * 管理版本配置的读取、缓存和错误响应生成
 * 提供版本验证相关的业务逻辑
 */
class VersionConfigService
{
    /**
     * 缓存键前缀
     */
    private const CACHE_PREFIX = 'version_config_';

    /**
     * 默认缓存时间（秒）
     */
    private const DEFAULT_CACHE_TTL = 3600;

    /**
     * 获取最小支持版本号
     *
     * @return string
     */
    public function getMinVersion(): string
    {
        return $this->getCachedConfig('min_supported_version', '2.1.3');
    }

    /**
     * 获取最大支持版本号
     *
     * @return string
     */
    public function getMaxVersion(): string
    {
        return $this->getCachedConfig('max_supported_version', '2.3.5');
    }

    /**
     * 获取当前最新版本号
     *
     * @return string
     */
    public function getCurrentLatestVersion(): string
    {
        return $this->getCachedConfig('current_latest_version', '2.3.5');
    }

    /**
     * 检查版本验证是否启用
     *
     * @return bool
     */
    public function isValidationEnabled(): bool
    {
        return (bool) $this->getCachedConfig('validation_enabled', true);
    }

    /**
     * 检查是否启用严格模式
     *
     * @return bool
     */
    public function isStrictModeEnabled(): bool
    {
        return (bool) $this->getCachedConfig('strict_mode', true);
    }

    /**
     * 检查版本号是否在支持范围内
     *
     * @param string $version 要检查的版本号
     * @return bool
     */
    public function isVersionInRange(string $version): bool
    {
        // 如果版本验证未启用，直接返回 true
        if (!$this->isValidationEnabled()) {
            return true;
        }

        if (!VersionComparator::isValidFormat($version)) {
            return false;
        }

        $minVersion = $this->getMinVersion();
        $maxVersion = $this->getMaxVersion();

        return VersionComparator::isInRange($version, $minVersion, $maxVersion);
    }

    /**
     * 获取版本验证结果
     *
     * @param string $version 要验证的版本号
     * @return array 验证结果 ['valid' => bool, 'error_code' => int|null, 'error_type' => string|null]
     */
    public function validateVersion(string $version): array
    {
        // 如果版本验证未启用，直接返回有效结果
        if (!$this->isValidationEnabled()) {
            return [
                'valid' => true,
                'error_code' => null,
                'error_type' => null,
                'validation_disabled' => true
            ];
        }

        // 检查版本号格式
        if (!VersionComparator::isValidFormat($version)) {
            return [
                'valid' => false,
                'error_code' => $this->getErrorCode('version_invalid_format'),
                'error_type' => 'invalid_format'
            ];
        }

        $minVersion = $this->getMinVersion();
        $maxVersion = $this->getMaxVersion();

        // 检查版本是否过低
        if (VersionComparator::isLowerThan($version, $minVersion)) {
            return [
                'valid' => false,
                'error_code' => $this->getErrorCode('version_too_low'),
                'error_type' => 'too_low'
            ];
        }

        // 检查版本是否过高
        if (VersionComparator::isGreaterThan($version, $maxVersion)) {
            return [
                'valid' => false,
                'error_code' => $this->getErrorCode('version_too_high'),
                'error_type' => 'too_high'
            ];
        }

        return [
            'valid' => true,
            'error_code' => null,
            'error_type' => null
        ];
    }

    /**
     * 生成版本错误响应数据
     *
     * @param string $currentVersion 当前版本号
     * @param string $errorType 错误类型
     * @return array
     */
    public function getVersionErrorResponse(string $currentVersion, string $errorType = 'too_low'): array
    {
        $errorCode = $this->getErrorCode("version_{$errorType}");
        $message = $this->getErrorMessage("version_{$errorType}");

        $responseData = [
            'status' => 400,
            'code' => $errorCode,
            'msg' => $message,
            'data' => [
                'current_version' => $currentVersion,
                'min_version' => $this->getMinVersion(),
                'max_version' => $this->getMaxVersion(),
                'latest_version' => $this->getCurrentLatestVersion(),
                'upgrade_instruction' => $this->getErrorMessage('upgrade_instruction')
            ]
        ];

        return $responseData;
    }

    /**
     * 生成版本缺失错误响应
     *
     * @return array
     */
    public function getVersionMissingErrorResponse(): array
    {
        return [
            'status' => 400,
            'code' => $this->getErrorCode('version_missing'),
            'msg' => $this->getErrorMessage('version_missing'),
            'data' => [
                'min_version' => $this->getMinVersion(),
                'max_version' => $this->getMaxVersion(),
                'latest_version' => $this->getCurrentLatestVersion(),
                'upgrade_instruction' => $this->getErrorMessage('upgrade_instruction')
            ]
        ];
    }

    /**
     * 获取版本配置摘要信息
     *
     * @return array
     */
    public function getVersionConfigSummary(): array
    {
        return [
            'min_version' => $this->getMinVersion(),
            'max_version' => $this->getMaxVersion(),
            'latest_version' => $this->getCurrentLatestVersion(),
            'validation_enabled' => $this->isValidationEnabled(),
            'strict_mode' => $this->isStrictModeEnabled(),
            'cache_enabled' => $this->isCacheEnabled(),
            'cache_ttl' => $this->getCacheTtl()
        ];
    }

    /**
     * 清除版本配置缓存
     *
     * @return bool
     */
    public function clearCache(): bool
    {
        $keys = [
            'min_supported_version',
            'max_supported_version',
            'current_latest_version',
            'validation_enabled'
        ];

        $cleared = true;
        foreach ($keys as $key) {
            $cacheKey = self::CACHE_PREFIX . $key;
            if (!Cache::forget($cacheKey)) {
                $cleared = false;
            }
        }

        return $cleared;
    }

    /**
     * 预热版本配置缓存
     *
     * @return void
     */
    public function warmupCache(): void
    {
        $this->getMinVersion();
        $this->getMaxVersion();
        $this->getCurrentLatestVersion();
        $this->isValidationEnabled();
    }

    /**
     * 获取带缓存的配置值
     *
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    private function getCachedConfig(string $key, $default = null)
    {
        if (!$this->isCacheEnabled()) {
            return Config::get("version.{$key}", $default);
        }

        $cacheKey = self::CACHE_PREFIX . $key;
        $cacheTtl = $this->getCacheTtl();

        return Cache::remember($cacheKey, $cacheTtl, function () use ($key, $default) {
            return Config::get("version.{$key}", $default);
        });
    }

    /**
     * 获取错误码
     *
     * @param string $errorType 错误类型
     * @return int
     */
    private function getErrorCode(string $errorType): int
    {
        return (int) Config::get("version.error_codes.{$errorType}", 2000);
    }

    /**
     * 获取错误消息
     *
     * @param string $messageType 消息类型
     * @return string
     */
    private function getErrorMessage(string $messageType): string
    {
        return Config::get("version.messages.{$messageType}", '版本验证失败');
    }

    /**
     * 检查缓存是否启用
     *
     * @return bool
     */
    private function isCacheEnabled(): bool
    {
        return (bool) Config::get('version.cache.enabled', true);
    }

    /**
     * 获取缓存TTL
     *
     * @return int
     */
    private function getCacheTtl(): int
    {
        return (int) Config::get('version.cache.ttl', self::DEFAULT_CACHE_TTL);
    }

    /**
     * 批量验证多个版本号
     *
     * @param array $versions 版本号数组
     * @return array 验证结果
     */
    public function validateMultipleVersions(array $versions): array
    {
        $results = [];
        
        foreach ($versions as $version) {
            $results[$version] = $this->validateVersion($version);
        }

        return $results;
    }

    /**
     * 获取版本统计信息
     *
     * @param array $versions 版本号数组
     * @return array 统计信息
     */
    public function getVersionStatistics(array $versions): array
    {
        $stats = [
            'total' => count($versions),
            'valid' => 0,
            'invalid' => 0,
            'too_low' => 0,
            'too_high' => 0,
            'invalid_format' => 0,
            'validation_enabled' => $this->isValidationEnabled()
        ];

        foreach ($versions as $version) {
            $result = $this->validateVersion($version);
            
            if ($result['valid']) {
                $stats['valid']++;
            } else {
                $stats['invalid']++;
                
                switch ($result['error_type']) {
                    case 'too_low':
                        $stats['too_low']++;
                        break;
                    case 'too_high':
                        $stats['too_high']++;
                        break;
                    case 'invalid_format':
                        $stats['invalid_format']++;
                        break;
                }
            }
        }

        return $stats;
    }

    /**
     * 快速检查是否需要执行版本验证
     * 
     * @return bool
     */
    public function shouldValidateVersion(): bool
    {
        return $this->isValidationEnabled();
    }

    /**
     * 启用版本验证
     * 注意：这只是临时启用，不会修改配置文件
     * 
     * @return void
     */
    public function enableValidation(): void
    {
        // 清除相关缓存，强制重新读取配置
        $cacheKey = self::CACHE_PREFIX . 'validation_enabled';
        Cache::put($cacheKey, true, $this->getCacheTtl());
    }

    /**
     * 禁用版本验证
     * 注意：这只是临时禁用，不会修改配置文件
     * 
     * @return void
     */
    public function disableValidation(): void
    {
        // 清除相关缓存，强制重新读取配置
        $cacheKey = self::CACHE_PREFIX . 'validation_enabled';
        Cache::put($cacheKey, false, $this->getCacheTtl());
    }

    /**
     * 重置验证状态到配置文件设置
     * 
     * @return void
     */
    public function resetValidationState(): void
    {
        $cacheKey = self::CACHE_PREFIX . 'validation_enabled';
        Cache::forget($cacheKey);
    }
}