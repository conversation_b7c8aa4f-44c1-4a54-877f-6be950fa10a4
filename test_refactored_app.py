#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后应用程序功能测试脚本
验证重构后的功能完整性
"""

import sys
import os
import tempfile

def test_app_initializer():
    """测试应用程序初始化器"""
    print("=== 测试应用程序初始化器 ===")
    
    try:
        # 测试导入
        from utils.app_initializer import AppInitializer, initialize_app, cleanup_log_files
        print("✓ 成功导入应用程序初始化器模块")
        
        # 测试初始化器实例化
        initializer = AppInitializer()
        print("✓ 成功创建初始化器实例")
        
        # 测试Python路径设置
        initializer.setup_python_path()
        print("✓ Python路径设置完成")
        
        # 测试启动检查
        check_result = initializer.perform_startup_checks()
        print(f"✓ 启动检查结果: {check_result}")
        
        # 测试日志清理功能
        cleanup_log_files()
        print("✓ 日志清理功能测试完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用程序初始化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_cleanup():
    """测试日志清理功能"""
    print("\n=== 测试日志清理功能 ===")
    
    try:
        from config.settings import AppSettings
        from utils.app_initializer import cleanup_log_files
        
        # 获取日志文件路径
        log_file_path = AppSettings.get_log_file_path()
        print(f"日志文件路径: {log_file_path}")
        
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file_path)
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建测试日志文件
        with open(log_file_path, 'w', encoding='utf-8') as f:
            f.write("测试日志内容\n")
        print("✓ 创建测试日志文件")
        
        # 验证文件存在
        if os.path.exists(log_file_path):
            print("✓ 日志文件存在")
        else:
            print("✗ 日志文件不存在")
            return False
        
        # 测试清理功能
        cleanup_log_files()
        
        # 验证文件已被删除
        if not os.path.exists(log_file_path):
            print("✓ 日志文件已成功清理")
            return True
        else:
            print("✗ 日志文件清理失败")
            return False
            
    except Exception as e:
        print(f"✗ 日志清理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_detection():
    """测试环境检测功能"""
    print("\n=== 测试环境检测功能 ===")
    
    try:
        from utils.helpers import is_development_environment
        from config.settings import AppSettings
        
        # 测试环境检测
        is_dev = is_development_environment()
        print(f"当前环境: {'开发环境' if is_dev else '打包环境'}")
        
        # 测试动态配置
        log_output = AppSettings.get_default_log_output()
        api_url = AppSettings.get_api_base_url()
        
        print(f"动态日志输出模式: {log_output}")
        print(f"动态API基础URL: {api_url}")
        
        # 验证配置是否符合预期
        if is_dev:
            expected_log = AppSettings.LOG_OUTPUT_FILE
            expected_api = "http://tsa.test.com/api"
        else:
            expected_log = AppSettings.LOG_OUTPUT_SILENT
            expected_api = "https://trade.dailyhotnews.com.cn/api"
        
        if log_output == expected_log and api_url == expected_api:
            print("✓ 动态配置正确")
            return True
        else:
            print("✗ 动态配置不正确")
            return False
            
    except Exception as e:
        print(f"✗ 环境检测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_structure():
    """测试main.py结构"""
    print("\n=== 测试main.py结构 ===")
    
    try:
        # 读取main.py文件内容
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含不应该存在的业务逻辑
        if 'cleanup_log_file' in content and 'def cleanup_log_file' in content:
            print("✗ main.py仍包含日志清理业务逻辑")
            return False
        
        # 检查是否正确导入初始化模块
        if 'from utils.app_initializer import initialize_app' in content:
            print("✓ 正确导入初始化模块")
        else:
            print("✗ 未正确导入初始化模块")
            return False
        
        # 检查main函数是否简洁
        lines = content.split('\n')
        main_function_lines = []
        in_main_function = False
        
        for line in lines:
            if line.strip().startswith('def main():'):
                in_main_function = True
            elif in_main_function and line.strip().startswith('def '):
                break
            elif in_main_function:
                main_function_lines.append(line)
        
        # 主函数应该相对简洁（不超过30行）
        if len(main_function_lines) <= 30:
            print("✓ main函数保持简洁")
            return True
        else:
            print(f"✗ main函数过于复杂（{len(main_function_lines)}行）")
            return False
            
    except Exception as e:
        print(f"✗ main.py结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("重构后应用程序功能测试")
    print("=" * 40)
    
    tests = [
        test_app_initializer,
        test_log_cleanup,
        test_environment_detection,
        test_main_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，重构成功！")
        return True
    else:
        print("✗ 部分测试失败，需要检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
