#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理面板组件
"""

from PyQt5.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QComboBox, QGridLayout, 
                             QSpinBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database.config_dao import ConfigDAO
from ui.worker_threads import ConfigValidationWorker
from utils.logger import log_message
from utils.helpers import get_dpi_scale_factor


class ConfigPanel(QGroupBox):
    """配置管理面板组件"""
    
    # 信号定义
    config_validated = pyqtSignal(bool, str, dict, str, str, str)  # 验证完成信号
    config_changed = pyqtSignal(str, str)  # 配置改变信号
    validation_error_occurred = pyqtSignal(str)  # 验证错误信号
    
    def __init__(self, config_dao: ConfigDAO, parent=None):
        super().__init__("配置管理", parent)
        self.config_dao = config_dao
        self.validation_worker = None
        self._loading_configs = False
        
        # 全局变量缓存当前配置
        self.current_appid = ""
        self.current_appsecret = ""
        
        self.init_ui()
        

        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        # 根据DPI动态调整边距和间距
        dpi_scale = get_dpi_scale_factor()
        margin = max(8, int(8 * dpi_scale))
        v_spacing = max(4, int(4 * dpi_scale))
        h_spacing = max(6, int(6 * dpi_scale))
        
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(v_spacing)

        # 配置选择行
        config_select_layout = QHBoxLayout()
        config_select_layout.setSpacing(h_spacing)
        config_select_layout.addWidget(QLabel("选择配置:"))

        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(200)
        self.config_combo.currentTextChanged.connect(self.on_config_changed)
        config_select_layout.addWidget(self.config_combo)

        self.delete_config_btn = QPushButton("删除配置")
        self.delete_config_btn.clicked.connect(self.delete_current_config)
        config_select_layout.addWidget(self.delete_config_btn)

        config_select_layout.addStretch()

        # 当前账号显示标签
        self.current_account_label = QLabel("")
        self.current_account_label.setStyleSheet("color: #2E8B57; font-weight: bold; font-size: 12px;")
        self.current_account_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        config_select_layout.addWidget(self.current_account_label)

        layout.addLayout(config_select_layout)

        # 配置输入区域 - 使用网格布局更紧凑，支持DPI缩放
        input_layout = QGridLayout()
        # 使用已计算的间距值
        input_layout.setSpacing(v_spacing)

        # AppID输入
        input_layout.addWidget(QLabel("AppID:"), 0, 0)
        self.appid_input = QLineEdit()
        self.appid_input.setPlaceholderText("从跨境蜂系统中获取")
        input_layout.addWidget(self.appid_input, 0, 1)

        # AppSecret输入
        input_layout.addWidget(QLabel("AppSecret:"), 1, 0)
        self.appsecret_input = QLineEdit()
        self.appsecret_input.setPlaceholderText("从跨境蜂系统中获取")
        self.appsecret_input.setEchoMode(QLineEdit.Password)
        input_layout.addWidget(self.appsecret_input, 1, 1)

        # Memo输入
        input_layout.addWidget(QLabel("备注:"), 2, 0)
        self.memo_input = QLineEdit()
        self.memo_input.setPlaceholderText("用于识别配置用途")
        input_layout.addWidget(self.memo_input, 2, 1)

        # 单次获取文件个数输入
        input_layout.addWidget(QLabel("单次获取文件个数:"), 3, 0)
        self.concurrent_count_input = QSpinBox()
        self.concurrent_count_input.setMinimum(1)
        self.concurrent_count_input.setMaximum(10)
        self.concurrent_count_input.setValue(5)
        self.concurrent_count_input.setToolTip("设置同时下载的文件数量，范围：1-10")
        self.concurrent_count_input.setMaximumWidth(80)  # 设置最大宽度，足够显示4位数字
        self.concurrent_count_input.valueChanged.connect(self.on_concurrent_count_changed)
        input_layout.addWidget(self.concurrent_count_input, 3, 1)

        # 设置列宽比例
        input_layout.setColumnStretch(0, 0)
        input_layout.setColumnStretch(1, 1)

        layout.addLayout(input_layout)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(h_spacing)

        self.validate_btn = QPushButton("验证")
        self.validate_btn.clicked.connect(self.validate_config)
        button_layout.addWidget(self.validate_btn)

        self.process_btn = QPushButton("处理商品数据")
        self.process_btn.clicked.connect(self.process_goods_data)
        button_layout.addWidget(self.process_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 移除固定最大高度限制，让组件根据内容自适应高度
        # self.setMaximumHeight(160)  # 已注释，支持高DPI缩放
        
    def load_configs(self):
        """加载配置到下拉框"""
        try:
            # 设置加载标志，避免触发配置改变事件
            self._loading_configs = True

            configs = self.config_dao.get_all_configs()

            self.config_combo.clear()
            self.config_combo.addItem("-- 新建配置 --", None)

            default_index = 0

            for i, config in enumerate(configs):
                display_text = f"{config['appid']}"
                if config['memo']:
                    display_text += f" ({config['memo']})"

                self.config_combo.addItem(display_text, config)

                # 如果是默认配置，记录索引
                if config.get('is_default') == 1:
                    default_index = i + 1

            # 如果没有默认配置但有配置存在，选择第一个配置
            if default_index == 0 and len(configs) > 0:
                default_index = 1
                # 设置第一个配置为默认
                try:
                    self.config_dao.set_default_config(configs[0]['id'])
                except Exception as e:
                    log_message(f"设置默认配置失败: {str(e)}", "ERROR")

            # 选择默认配置
            self.config_combo.setCurrentIndex(default_index)

            log_message(f"已加载 {len(configs)} 个配置")

            # 更新当前账号显示
            if default_index > 0:
                current_data = self.config_combo.currentData()
                if current_data:
                    self.update_current_account_display(current_data)

        except Exception as e:
            log_message(f"加载配置失败: {str(e)}", "ERROR")
        finally:
            # 清除加载标志
            self._loading_configs = False
            
    def on_config_changed(self):
        """配置选择改变事件"""
        current_data = self.config_combo.currentData()

        if current_data is None:
            # 新建配置
            self.appid_input.clear()
            self.appsecret_input.clear()
            self.memo_input.clear()
            self.concurrent_count_input.setValue(5)
            self.current_appid = ""
            self.current_appsecret = ""
            self.delete_config_btn.setEnabled(False)
            self.update_current_account_display(None)
        else:
            # 加载现有配置
            self.appid_input.setText(current_data['appid'])
            self.appsecret_input.setText(current_data['appsecret'])
            self.memo_input.setText(current_data['memo'] or "")
            concurrent_count = current_data.get('concurrent_download_count', 5)
            if concurrent_count is None:
                concurrent_count = 5
            self.concurrent_count_input.setValue(concurrent_count)
            self.current_appid = current_data['appid']
            self.current_appsecret = current_data['appsecret']
            self.delete_config_btn.setEnabled(True)

            self.update_current_account_display(current_data)

            # 设置为默认账号（当用户主动选择时）
            if not self._loading_configs:
                try:
                    self.config_dao.set_default_config(current_data['id'])
                    log_message(f"已设置默认账号: {current_data['appid']}")
                except Exception as e:
                    log_message(f"设置默认账号失败: {str(e)}", "ERROR")
                    
        # 发射配置改变信号
        self.config_changed.emit(self.current_appid, self.current_appsecret)

    def on_concurrent_count_changed(self, value):
        """并发下载数量变更事件"""
        if self.current_appid:
            try:
                success = self.config_dao.update_concurrent_download_count(self.current_appid, value)
                if success:
                    log_message(f"并发下载数量已更新为: {value}", "INFO")
                else:
                    log_message("更新并发下载数量失败", "ERROR")
            except Exception as e:
                log_message(f"更新并发下载数量时出错: {str(e)}", "ERROR")

    def validate_config(self):
        """验证配置"""
        appid = self.appid_input.text().strip()
        appsecret = self.appsecret_input.text().strip()
        memo = self.memo_input.text().strip()

        if not appid or not appsecret:
            QMessageBox.warning(self, "警告", "请输入AppID和AppSecret")
            return

        log_message("正在验证配置...")
        self.validate_btn.setEnabled(False)

        # 创建并启动验证工作线程
        self.validation_worker = ConfigValidationWorker(appid, appsecret)
        self.validation_worker.validation_completed.connect(
            lambda is_valid, error_msg, user_data: self.on_validation_completed(
                is_valid, error_msg, user_data, appid, appsecret, memo))
        self.validation_worker.error_occurred.connect(self.validation_error_occurred.emit)  # 转发错误信号
        self.validation_worker.start()

    def on_validation_completed(self, is_valid: bool, error_msg: str, user_data: dict,
                              appid: str, appsecret: str, memo: str):
        """验证完成回调"""
        try:
            if is_valid:
                log_message("配置验证成功", "SUCCESS")

                # 提取用户信息
                user_id = user_data.get('user_id') if user_data else None
                phone = user_data.get('phone') if user_data else None

                # 记录用户信息到日志
                if user_id:
                    log_message(f"用户ID: {user_id}")
                if phone:
                    log_message(f"手机号: {phone}")

                # 获取并发下载数量
                concurrent_count = self.concurrent_count_input.value()

                # 保存或更新配置
                if self.config_dao.config_exists(appid):
                    # 更新现有配置
                    self.config_dao.update_config_by_appid(appid, appsecret, memo, user_id, phone, True, concurrent_count)
                    log_message("配置已更新", "SUCCESS")
                else:
                    # 新增配置，设为默认账号
                    self.config_dao.insert_config(appid, appsecret, memo, user_id, phone, True, concurrent_count)
                    log_message("配置已保存", "SUCCESS")

                # 更新全局变量
                self.current_appid = appid
                self.current_appsecret = appsecret

                # 刷新下拉框
                self.load_configs()

                QMessageBox.information(self, "成功", "配置验证成功并已保存")
                
                # 发射验证完成信号
                self.config_validated.emit(is_valid, error_msg, user_data, appid, appsecret, memo)
            else:
                log_message(f"配置验证失败: {error_msg}", "ERROR")
                QMessageBox.critical(self, "错误", f"配置验证失败: {error_msg}")
                self.config_validated.emit(is_valid, error_msg, user_data, appid, appsecret, memo)

        except Exception as e:
            log_message(f"保存配置出错: {str(e)}", "ERROR")
            QMessageBox.critical(self, "错误", f"保存配置出错: {str(e)}")
        finally:
            self.validate_btn.setEnabled(True)
            self.validation_worker = None

    def delete_current_config(self):
        """删除当前选中的配置"""
        current_data = self.config_combo.currentData()
        if current_data is None:
            QMessageBox.warning(self, "警告", "请选择要删除的配置")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除配置 {current_data['appid']} 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.config_dao.delete_config(current_data['id'])
                log_message(f"已删除配置: {current_data['appid']}", "SUCCESS")
                self.load_configs()
                QMessageBox.information(self, "成功", "配置已删除")
            except Exception as e:
                log_message(f"删除配置失败: {str(e)}", "ERROR")
                QMessageBox.critical(self, "错误", f"删除配置失败: {str(e)}")

    def process_goods_data(self):
        """处理商品数据 - 这个方法将由主窗口处理"""
        # 这个方法的实现将在主窗口中处理
        pass

    def update_current_account_display(self, config_data: dict):
        """更新当前账号显示"""
        if not config_data:
            self.current_account_label.setText("")
            return

        # 获取手机号和备注
        phone = config_data.get('phone', '')
        memo = config_data.get('memo', '')

        # 构建显示文本
        display_text = "当前账号 "

        if phone:
            display_text += phone
        else:
            appid = config_data.get('appid', '')
            display_text += appid

        if memo:
            display_text += f"【{memo}】"

        self.current_account_label.setText(display_text)
        
    def get_current_config(self):
        """获取当前配置"""
        return self.current_appid, self.current_appsecret
        
    def set_controls_enabled(self, enabled: bool):
        """设置控件启用状态"""
        self.validate_btn.setEnabled(enabled)
        self.process_btn.setEnabled(enabled)
        self.config_combo.setEnabled(enabled)
        self.delete_config_btn.setEnabled(enabled)
        self.appid_input.setEnabled(enabled)
        self.appsecret_input.setEnabled(enabled)
        self.memo_input.setEnabled(enabled)
