import {ElMessage} from "element-plus";
import config from "@/config/index";
import * as pageMsg from '@/utils/page_msg';
import * as utils_new from '@/utils/new/utils';
// 引入解密方法
import axios from '@/api/axios';
import * as myProtobuf from '@/protobuf/myProtobuf';
import * as dyCreator from '@/utils/protocol/dy_creator/dy_creator';
import * as protobuf from 'protobufjs';
import Long from 'long';
protobuf.util.Long = Long;
protobuf.configure();

import { loadDouyinProto } from '@/protobuf/douyinProto';
import DouyinMessageParser from '@/protobuf/douyinMessageParser';
import { setTokenSourceMapRange } from "typescript";

// 导出版本管理器
export { versionManager, VersionManager } from './versionManager';
export type { Version, VersionCache } from './versionManager';

let douyinProto: any;
let douyinEnums: any;
let douyinMessageParser: DouyinMessageParser | null = null;

let messageBuffer: any[] = [];
let sendIntervalId: NodeJS.Timeout | null = null;
const SEND_INTERVAL = 30000; // 30秒

async function getWebCookie(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!url) {
      reject(new Error('URL is required'));
      return;
    }

    chrome.cookies.getAll({ url }, (cookies) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      const cookieArray = cookies.map(cookie => `${cookie.name}=${cookie.value}`);
      const cookieString = cookieArray.join('; ');
      resolve(cookieString);
    });
  });
}


function startSendInterval(tabId:string) {
  if (sendIntervalId === null) {
    sendIntervalId = setInterval(() => sendBufferedMessages(tabId), SEND_INTERVAL);
    console.log('Started message sending interval');
  }
}

function stopSendInterval(tabId:string) {
  if (sendIntervalId !== null) {
    clearInterval(sendIntervalId);
    sendIntervalId = null;
    console.log('Stopped message sending interval');
  }
}

export const getMonthByChinese = (month:string)=>{
  month = month.replace('月','');
  let month_arr = ['一','二','三','四','五','六','七','八','九','十','十一','十二'];
  for(let i=0;i<month_arr.length;i++){
    if(month_arr[i]==month){
      return i+1;
    }
  }
  return 0;
}

export const getCookies = async (url:any) => {
  return new Promise((resolve, reject) => {
    chrome.cookies.getAll({ url }, function(cookies:any) {
      const resList = cookies.map((item:any) => `${item.name}=${item.value}`);
      const cookieStr = resList.join(";");
      resolve(cookieStr);
    });
  });
}

export const getLivingRoomIndex = ()=>{
  return config.urlPage.living_room_index;
}

export const getQueryParams = (url: string): {[key: string]: string} =>{
  let queryParams: {[key: string]: string} = {};
  let urlParts = url.split('?');
  if (urlParts.length >= 2) {
      let params = urlParts[1].split('&');
      params.forEach(param => {
          let keyValue = param.split('=');
          queryParams[decodeURIComponent(keyValue[0])] = decodeURIComponent(keyValue[1] || '');
      });
  }
  return queryParams;
}

export const showMsg = (msg: string, type: number=1) => {
  if(type == 1){
    ElMessage.success(msg)
  }else if(type==2){
    ElMessage.error(msg)
  }else if(type==3){
    ElMessage.warning(msg)
  }
}
// @ts-ignore
export const getDate = ()=>{
  let date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day  = date.getDate();
  return year + '-' + month + '-' + day;
}

//验证字符串
export const validData = (str:string)=>{
  if(typeof str == 'undefined' || str == ''){
    return false;
  }
  return true;
}
export const randomString = (len:number=15)=>{
  len = len || 15;
  let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
  let maxPos = $chars.length;
  let pwd = '';
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}
export const clearData = (fetchAllData:any,date_key:string)=>{
  for (let key in fetchAllData) {
    if (key != date_key) {
    delete fetchAllData[key];
    }
  }
}
export const clearAllData = (fetchAllData:any,date_key?:string) => {
  for (let key in fetchAllData) {
    console.log('delete key====',key);
    delete fetchAllData[key];
  }
}
export const getWDomain = (targetOriginWithUrl:string) => {
  let domain = targetOriginWithUrl.split(';');
  domain.forEach(function (i, b, c) {
    c[b] = i.replace(/\./ig, "\\.");
  });
  let domains = domain.join("|");
  let w_Domain = new RegExp("^https?:\\/\\/(" + domains + ")");
  return w_Domain;
}
export const getBDomain = (targetOriginWithUrl:string) => {
  let domain = targetOriginWithUrl.split(';');
  domain.forEach(function (i, b, c) {
    c[b] = i.replace(/\./ig, "\\.");
  });
  let domains = domain.join("|");
  let b_Domain = new RegExp("^https?:\\/\\/(?!" + domains + ")");
  return b_Domain;
}

/**
 * 判断请求是否需要加密
 * 当前是开发模式或者请求的URL主机名是tsa.test.com时不加密，其他情况加密
 * @param url 请求的URL
 * @returns boolean 是否需要加密
 */
export const shouldEncryptRequest = (url: string): boolean => {
  // 判断是否是开发模式
  const isDevelopment = process.env.NODE_ENV === 'development';
  // 判断URL主机名是否是tsa.test.com
  let isTestDomain = false;
  try {
    const urlObj = new URL(url);
    isTestDomain = urlObj.hostname === 'tsa.test.com';
  } catch (error) {
    console.error('解析URL失败:', error);
    // URL解析失败，默认不是测试域名
  }

  console.log("isDevelopment  ",isDevelopment)
  console.log("isTestDomain  ",isTestDomain)
  return true;
  // 如果是开发模式或者是测试域名，则不加密
  return !(isDevelopment || isTestDomain);
}

export const getPostData = async (url:any,data: any, headers: any) => {
  try {
    const response = await axios.post(url, data, { headers });
    return response?.data || undefined;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};

export const getQueryData = async (url:any, headers: any) => {
  try {
    const response = await axios.get(url, { headers });
    return response?.data || undefined;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};