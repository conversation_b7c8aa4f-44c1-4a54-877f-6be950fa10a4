/**
 * 用户验证工具
 * 用于验证用户权限和功能访问控制
 */

import { userInfo } from '@/utils/userStore';

/**
 * 批量采集功能授权用户ID列表
 */
const BATCH_COLLECTION_AUTHORIZED_USER_IDS = [1, 60];

/**
 * 检查当前用户是否有权限使用批量采集功能
 * @returns boolean
 */
export const isUserAuthorizedForBatchCollection = (): boolean => {
  try {
    // 检查用户是否登录
    if (!userInfo || !userInfo.isLogin) {
      console.log('用户未登录，无法使用批量采集功能');
      return false;
    }

    // 检查用户ID是否在授权列表中
    const userId = userInfo.userId;
    const isAuthorized = userId ? BATCH_COLLECTION_AUTHORIZED_USER_IDS.includes(userId) : false;

    console.log(`用户ID: ${userId}, 批量采集权限: ${isAuthorized}`);

    return isAuthorized;
  } catch (error) {
    console.error('检查用户批量采集权限时出错:', error);
    return false;
  }
}

/**
 * 获取授权用户ID列表
 * @returns number[]
 */
export const getAuthorizedUserIds = (): number[] => {
  return [...BATCH_COLLECTION_AUTHORIZED_USER_IDS];
}

/**
 * 添加授权用户ID
 * @param userId 用户ID
 */
export const addAuthorizedUserId = (userId: number): void => {
  if (!BATCH_COLLECTION_AUTHORIZED_USER_IDS.includes(userId)) {
    BATCH_COLLECTION_AUTHORIZED_USER_IDS.push(userId);
    console.log(`已添加授权用户ID: ${userId}`);
  }
}

/**
 * 移除授权用户ID
 * @param userId 用户ID
 */
export const removeAuthorizedUserId = (userId: number): void => {
  const index = BATCH_COLLECTION_AUTHORIZED_USER_IDS.indexOf(userId);
  if (index > -1) {
    BATCH_COLLECTION_AUTHORIZED_USER_IDS.splice(index, 1);
    console.log(`已移除授权用户ID: ${userId}`);
  }
}

/**
 * 检查用户是否有权限使用指定功能
 * @param featureName 功能名称
 * @returns boolean
 */
export const isUserAuthorizedForFeature = (featureName: string): boolean => {
  switch (featureName) {
    case 'batch_collection':
      return isUserAuthorizedForBatchCollection();
    default:
      console.warn(`未知功能: ${featureName}`);
      return false;
  }
}
