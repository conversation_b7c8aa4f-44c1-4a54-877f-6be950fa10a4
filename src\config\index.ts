import { productionConfig } from './modules/production.config';
import { development1Config } from './modules/development_1.config';
import { developmentConfig } from './modules/development.config';

type Environment = 'production' | 'development_1' | 'development';

type ApiConfig = typeof productionConfig;

const getEnvironment = (): Environment => {
  const env = process.env.NODE_ENV as Environment;
  
  if (env === 'production') {
    return 'production';
  }else{
    if(env === 'development_1' || env === 'development'){
      return 'development';
    }
  }
  return 'development';
};

const getApiConfig = (): ApiConfig => {
  const env = getEnvironment();
  
  switch (env) {
    case 'production':
      return productionConfig;
    case 'development_1':
      return development1Config;
    case 'development':
    default:
      return developmentConfig;
  }
};

const apiConfig = getApiConfig();

export default {
  ay: 'hU5IfoZGq43TPkWr',
  api: '*',
  allowedDomains: apiConfig.allowedDomains,
  n11DebugMode: apiConfig.n11DebugMode,
  apiLoginUrl: apiConfig.loginUrl,
  apiRegisterUrl: apiConfig.registerUrl,
  apiCaptchaUrl: apiConfig.captcha,
  apiTemuGoodsAddUrl: apiConfig.temuGoodsAddUrl,
  apiTemuCheckGoodsExistsUrl: apiConfig.temuCheckGoodsExistsUrl,
  apiTemuBatchCheckGoodsExistsUrl: apiConfig.temuBatchCheckGoodsExistsUrl,
  apiUserInfoUrl: apiConfig.userInfoUrl,
  apiUserLogoutUrl: apiConfig.userLogoutUrl,
  apiStoreListUrl: apiConfig.storeListUrl,
  apiStoreCreateUrl: apiConfig.storeCreateUrl,
  apiStoreUpdateUrl: apiConfig.storeUpdateUrl,
  apiStoreDeleteUrl: apiConfig.storeDeleteUrl,
  apiStoreDetailUrl: apiConfig.storeDetailUrl,
  apiStoreBatchUpdateUrl: apiConfig.storeBatchUpdateUrl,
  apiGoodsListUrl: apiConfig.goodsListUrl,
  apiGoodsAddUrl: apiConfig.goodsAddUrl,
  apiGoodsUpdateUrl: apiConfig.goodsUpdateUrl,
  apiGoodsDeleteUrl: apiConfig.goodsDeleteUrl,
  apiGoodsDetailUrl: apiConfig.goodsDetailUrl,
  apiGoodsBatchUpdateUrl: apiConfig.goodsBatchUpdateUrl,
  apiGoodsAdjustSkuPriceUrl: apiConfig.goodsAdjustSkuPriceUrl,
  apiGoodsPriceAdjustmentLogsUrl: apiConfig.goodsPriceAdjustmentLogsUrl,
  apiCatTemuWebpageMainUrl: apiConfig.catTemuWebpageMainUrl,
  apiCatTemuWebpageListUrl: apiConfig.catTemuWebpageListUrl,
  apiCatTemuMainUrl: apiConfig.catTemuMainUrl,
  apiCatTemuListUrl: apiConfig.catTemuListUrl,
  apiCatTemuListLazyUrl: apiConfig.catTemuListLazyUrl,
  apiCatTemuListflatUrl: apiConfig.catTemuListflatUrl,
  apiCatTemuChildrenCountUrl: apiConfig.catTemuChildrenCountUrl,
  apiCatTemuChildrenCountsUrl: apiConfig.catTemuChildrenCountsUrl,
  apiCatN11ListUrl: apiConfig.catN11ListUrl,
  apiCatN11DetailUrl: apiConfig.catN11DetailUrl,
  apiCatN11UpdateUrl: apiConfig.catN11UpdateUrl,
  apiCatRelationSetUrl: apiConfig.catRelationSetUrl,
  apiCatRelationListUrl: apiConfig.catRelationListUrl,
  apiUserStoreInfoUrl: apiConfig.userStoreInfoUrl,
  apiTaskAddUrl: apiConfig.taskAddUrl,
  apiTaskListUrl: apiConfig.taskListUrl,
  apiTaskStartUrl: apiConfig.taskStartUrl,
  apiTaskUpdateUrl: apiConfig.taskUpdateUrl,
  apiTaskDetailUrl: apiConfig.taskDetailUrl,
  apiTaskDetailListUrl: apiConfig.taskDetailListUrl,
  apiTaskQueryResultsUrl: apiConfig.taskQueryResultsUrl,
  apiTaskPendingQueryUrl: apiConfig.taskPendingQueryUrl,
  apiTaskBatchUpdateUrl: apiConfig.taskBatchUpdateUrl,
  apiTaskSaveUploadParamsUrl: apiConfig.taskSaveUploadParamsUrl,
  apiTaskRetryUploadParamsUrl: apiConfig.taskRetryUploadParamsUrl,
  apiTaskBatchRetryUploadUrl: apiConfig.taskBatchRetryUploadUrl,
  apiTaskBatchRetryUploadByStatusUrl: apiConfig.taskBatchRetryUploadByStatusUrl,
  apiTaskUpdateCategoryUrl: apiConfig.taskUpdateCategoryUrl,
  apiCatTemuDetailUrl: apiConfig.catTemuDetailUrl,
  apiGoodsDirectoryListUrl: apiConfig.goodsDirectoryListUrl,
  apiGoodsDirectoryCreateUrl: apiConfig.goodsDirectoryCreateUrl,
  apiGoodsDirectoryUpdateUrl: apiConfig.goodsDirectoryUpdateUrl,
  apiGoodsDirectoryDeleteUrl: apiConfig.goodsDirectoryDeleteUrl,
  apiGoodsDirectoryDetailUrl: apiConfig.goodsDirectoryDetailUrl,
  apiGoodsDirectoryBatchUpdateUrl: apiConfig.goodsDirectoryBatchUpdateUrl,
  apiGoodsDirectoryUpdateGoodsCountUrl: apiConfig.goodsDirectoryUpdateGoodsCountUrl,
  apiGoodsDirectoryUpdateAllGoodsCountUrl: apiConfig.goodsDirectoryUpdateAllGoodsCountUrl,
  apiUserCollectionSettingsUrl: apiConfig.userCollectionSettingsUrl,
  apiUserAvailableDirectoriesUrl: apiConfig.userAvailableDirectoriesUrl,
  apiUserCheckNeedSetupUrl: apiConfig.userCheckNeedSetupUrl,
  apiUserProcessGoodsImagesUrl: apiConfig.userProcessGoodsImagesUrl,
  apiGoodsNeedImageProcessUrl: apiConfig.goodsNeedImageProcessUrl,
  apiGoodsStatisticsUrl: apiConfig.goodsStatisticsUrl,
  apiGoodsCatRelationGetUrl: apiConfig.goodsCatRelationGetUrl,
  apiGoodsCatRelationSaveUrl: apiConfig.goodsCatRelationSaveUrl,
  apiGoodsCatRelationDeleteUrl: apiConfig.goodsCatRelationDeleteUrl,
  apiGoodsCatRelationBatchGetUrl: apiConfig.goodsCatRelationBatchGetUrl,
  apiKfUrl: apiConfig.kfUrl,
  apiCardCodeListUrl: apiConfig.cardCodeListUrl,
  apiCardCodeCreateUrl: apiConfig.cardCodeCreateUrl,
  apiCardCodeBatchCreateUrl: apiConfig.cardCodeBatchCreateUrl,
  apiCardCodeUpdateUrl: apiConfig.cardCodeUpdateUrl,
  apiCardCodeDeleteUrl: apiConfig.cardCodeDeleteUrl,
  apiCardCodeBatchDeleteUrl: apiConfig.cardCodeBatchDeleteUrl,
  apiCardCodeDetailUrl: apiConfig.cardCodeDetailUrl,
  apiCardCodeStatisticsUrl: apiConfig.cardCodeStatisticsUrl,
  apiCardCodeUseCardUrl: apiConfig.cardCodeUseCardUrl,
  apiCardCodeUserUsageRecordsUrl: apiConfig.cardCodeUserUsageRecordsUrl,
  apiCardCodeUsageRecordsUrl: apiConfig.cardCodeUsageRecordsUrl,
  apiCardCodeCardTypeOptionsUrl: apiConfig.cardCodeCardTypeOptionsUrl,
  apiCardCodeStatusOptionsUrl: apiConfig.cardCodeStatusOptionsUrl,
  apiCardCodeCopyUrl: apiConfig.cardCodeCopyUrl,
  apiCardCodeVipDaysUnitOptionsUrl: apiConfig.cardCodeVipDaysUnitOptionsUrl,
  apiCardCodeCopyStatusOptionsUrl: apiConfig.cardCodeCopyStatusOptionsUrl,
  apiCardCodeMemberOptionsUrl: apiConfig.cardCodeMemberOptionsUrl,
  apiAiKeyListUrl: apiConfig.aiKeyListUrl,
  apiAiKeyCreateUrl: apiConfig.aiKeyCreateUrl,
  apiAiKeyUpdateUrl: apiConfig.aiKeyUpdateUrl,
  apiAiKeyDeleteUrl: apiConfig.aiKeyDeleteUrl,
  apiAiKeyDetailUrl: apiConfig.aiKeyDetailUrl,
  apiAiKeyBatchUpdateUrl: apiConfig.aiKeyBatchUpdateUrl,
  apiN11RejectedProductBatchSaveUrl: apiConfig.n11RejectedProductBatchSaveUrl,
  apiN11RejectedProductPendingCountUrl: apiConfig.n11RejectedProductPendingCountUrl,
  apiN11RejectedProductNextPendingUrl: apiConfig.n11RejectedProductNextPendingUrl,
  apiN11RejectedProductUpdateStatusUrl: apiConfig.n11RejectedProductUpdateStatusUrl,
  apiN11RejectedProductBatchUpdateStatusUrl: apiConfig.n11RejectedProductBatchUpdateStatusUrl,
  apiN11RejectedProductStatisticsUrl: apiConfig.n11RejectedProductStatisticsUrl,
  apiTaskNewGenerateDetailsUrl: apiConfig.taskNewGenerateDetailsUrl,
  apiTaskNewNextAiTaskUrl: apiConfig.taskNewNextAiTaskUrl,
  apiTaskNewUpdateAiResultUrl: apiConfig.taskNewUpdateAiResultUrl,
  apiTaskNewRandomAiKeyUrl: apiConfig.taskNewRandomAiKeyUrl,
  apiTaskNewAiProgressUrl: apiConfig.taskNewAiProgressUrl,
  apiTaskNewCompleteAiTaskUrl: apiConfig.taskNewCompleteAiTaskUrl,
  apiPointsLogsUrl: apiConfig.pointsLogsUrl,
  apiPointsCheckTaskDeductedUrl: apiConfig.pointsCheckTaskDeductedUrl,
  apiGenerateCredentialsUrl: apiConfig.generateCredentialsUrl,
  apiSubAccountListUrl: apiConfig.subAccountListUrl,
  apiSubAccountCreateUrl: apiConfig.subAccountCreateUrl,
  apiSubAccountUpdateUrl: apiConfig.subAccountUpdateUrl,
  apiSubAccountDeleteUrl: apiConfig.subAccountDeleteUrl,
  apiSubAccountToggleStatusUrl: apiConfig.subAccountToggleStatusUrl,
  apiSubAccountDetailUrl: apiConfig.subAccountDetailUrl,
  apiSubAccountGoodsListUrl: apiConfig.subAccountGoodsListUrl,
  apiSubAccountGoodsDeleteUrl: apiConfig.subAccountGoodsDeleteUrl,
  apiChangePasswordUrl: apiConfig.changePasswordUrl,
  urlPage: {
    'living_room_index': 'https://leads.cluerich.com/pc/analysis/live-screen?fullscreen=0',
  },
};
