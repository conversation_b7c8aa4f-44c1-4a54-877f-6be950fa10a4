<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsPriceAdjustmentLogModel;
use App\Models\User\UserGoodsDirectoryModel;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;

class SubAccountGoodsService extends BaseService
{
    /**
     * 获取子账号商品列表（分页）
     * 主账号查询时查询所有user_id为当前账号id且status=1的商品
     * 子账号查询时查询user_id是主账户ID且user_sub_id是当前账号id且status=1的商品
     * 主账号可以通过sub_account_id参数查询特定子账号的商品
     */
    public function getSubAccountGoodsList(int $userId, int $userPid, array $params): array
    {


        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));

        // 获取筛选参数
        $type = $params['type'] ?? null;
        $status = $params['status'] ?? null;
        $goodsName = $params['goods_name'] ?? null;
        $goodsId = $params['goods_id'] ?? null;
        $directoryId = $params['directory_id'] ?? null;
        $subAccountId = $params['sub_account_id'] ?? null; // 主账号查询特定子账号的商品

        // 主账号专属筛选参数
        $priceAdjusted = $params['price_adjusted'] ?? null;
        $catAdjusted = $params['cat_adjusted'] ?? null;
        $onlySubAccount = $params['only_sub_account'] ?? false;
        $subAccountName = $params['sub_account_name'] ?? null;
        $subAccountPhone = $params['sub_account_phone'] ?? null;

        $timeType = $params['time_type'] ?? 'created_at';
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';

        // directory_id为可选参数，0或空表示不筛选目录

        // 检查当前用户是主账号还是子账号
        $isMainAccount = $this->isMainAccount($userPid);
        
        // 构建查询
        $query = GoodsModel::query();
        
        if ($isMainAccount) {
            // 主账号：查询所有user_id为当前账号id且status=1的商品
            $query->byUserId($userId)->byStatus(1);

            // 主账号专属筛选：仅显示子账号商品
            if ($onlySubAccount) {
                $query->where('user_sub_id', '>', 0);
            }

            // 如果指定了子账号ID，则只查询该子账号的商品
            if ($subAccountId) {
                $query->where('user_sub_id', $subAccountId);
            }

            // 主账号专属筛选：按子账号姓名筛选
            if ($subAccountName) {
                $query->whereHas('subAccount', function($q) use ($subAccountName) {
                    $q->where('name', 'like', "%{$subAccountName}%");
                });
            }

            // 主账号专属筛选：按子账号手机号筛选
            if ($subAccountPhone) {
                $query->whereHas('subAccount', function($q) use ($subAccountPhone) {
                    $q->where('phone', 'like', "%{$subAccountPhone}%");
                });
            }
        } else {
            // 子账号：查询user_id是主账户ID且user_sub_id是当前账号id且status=1的商品
            $query->where('user_id', $userPid)
                  ->where('user_sub_id', $userId)
                  ->byStatus(1);
        }

        // 按商品类型筛选
        if ($type && in_array($type, [1])) {
            $query->byType($type);
        }

        if ($goodsName) {
            $query->where('goods_name', 'like', "%{$goodsName}%");
        }

        if ($goodsId) {
            // 确保 goods_id 是数字类型
            $goodsIdInt = is_numeric($goodsId) ? (int)$goodsId : null;
            if ($goodsIdInt) {
                $query->where('goods_id', $goodsIdInt);
            }
        }

        // 按目录ID筛选（仅在有有效目录ID时筛选）
        if (is_numeric($directoryId) && $directoryId > 0) {
            $query->byDirectoryId($directoryId);
        }

        // 按时间范围筛选
        if ($startDate && $endDate) {
            // 确保日期格式正确
            try {
                $startDateTime = date('Y-m-d 00:00:00', strtotime($startDate));
                $endDateTime = date('Y-m-d 23:59:59', strtotime($endDate));



                if ($timeType === 'updated_at') {
                    $query->whereBetween('updated_at', [$startDateTime, $endDateTime]);
                } else {
                    $query->whereBetween('created_at', [$startDateTime, $endDateTime]);
                }
            } catch (\Exception $e) {
                Log::error('时间筛选参数格式错误', [
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 主账号专属筛选：按价格调整状态筛选
        if ($isMainAccount && $priceAdjusted !== null) {
            if ($priceAdjusted == 1) {
                // 已调整：is_price_modified = 1
                $query->where('is_price_modified', 1);
            } elseif ($priceAdjusted == 0) {
                // 未调整：is_price_modified = 0 或 null
                $query->where('is_price_modified', 0);
            }
        }

        // 主账号专属筛选：按分类调整状态筛选
        if ($isMainAccount && $catAdjusted !== null) {
            if ($catAdjusted == 1) {
                // 已调整：is_cat_modified = 1
                $query->where('is_cat_modified', 1);
            } elseif ($catAdjusted == 0) {
                $query->where('is_cat_modified', 0);
            }
        }

        // 排序
        $allowedSortFields = ['id', 'updated_at', 'created_at', 'goods_name'];
        $allowedSortOrders = ['asc', 'desc'];

        if (in_array($sortField, $allowedSortFields) && in_array($sortOrder, $allowedSortOrders)) {
            $query->orderBy($sortField, $sortOrder);
        } else {
            $query->orderBy('id', 'desc');
        }



        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;

        $goods = $query->offset($offset)
                      ->limit($pageSize)
                      ->with(['skus', 'frontCategory2', 'catRelations', 'userCatRelations', 'subAccount'])
                      ->get()
                      ->map(function($item) {
                          return $this->formatGoodsData($item);
                      });

        return [
            'list' => $goods,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 删除子账号商品（支持主账号和子账号删除）
     */
    public function deleteSubAccountGoods(int $userId, int $userPid, int $goodsId): array
    {
        // 检查当前用户是主账号还是子账号
        $isMainAccount = $this->isMainAccount($userPid);

        if ($isMainAccount) {
            // 主账号删除：可以删除该主账号下的所有商品
            $goods = GoodsModel::where('user_id', $userId)
                              ->where('id', $goodsId)
                              ->first();
        } else {
            // 子账号删除：只能删除自己的商品
            $goods = GoodsModel::where('user_id', $userPid)
                              ->where('user_sub_id', $userId)
                              ->where('id', $goodsId)
                              ->first();
        }

        if (!$goods) {
            throw new MyException('您没有权限删除该商品');
        }

        // 如果是子账号删除商品，需要验证商品的创建时间是否为今天
        if (!$isMainAccount) {
            $today = date('Y-m-d');
            $goodsCreateDate = date('Y-m-d', strtotime($goods->created_at));

            if ($goodsCreateDate !== $today) {
                throw new MyException('您只能删除今天获取的商品');
            }
        }

        // 记录商品所属目录ID，用于后续更新目录商品数量
        $directoryId = $goods->directory_id;
        $goodsUserId = $goods->user_id;

        // 执行软删除
        $goods->status = 0;
        $goods->save();

        // 重新计算商品目录总数
        if ($directoryId > 0) {
            $this->recalculateDirectoryGoodsCount($goodsUserId, $directoryId);
        }

        return ['success' => true];
    }

    /**
     * 检查用户是否为主账号 传递的是pid
     */
    private function isMainAccount(int $userPid): bool
    {
        return $userPid === 0;
    }

    /**
     * 重新计算指定目录的商品数量
     *
     * @param int $userId 用户ID 这个必须是主账号ID
     * @param int $directoryId 目录ID
     */
    private function recalculateDirectoryGoodsCount(int $userId, int $directoryId): void
    {
        // 统计该目录下的有效商品数量
        $goodsCount = GoodsModel::where('user_id', $userId)
            ->where('directory_id', $directoryId)
            ->where('status', 1) // 只统计有效商品
            ->count();

        // 更新目录的商品数量
        UserGoodsDirectoryModel::where('user_id', $userId)
            ->where('id', $directoryId)
            ->update(['goods_count' => $goodsCount]);
    }

    /**
     * 格式化商品数据（参考原始GoodsService的格式）
     */
    private function formatGoodsData($goods): array
    {
        $front_cat_2_path_name = $goods->frontCategory2?->path_name ?? '';
        $front_cat_2_path_name = str_ireplace(',', '->', $front_cat_2_path_name);
        $goods_pic = $goods->goods_pic;
        if(!empty($goods_pic)){
            $goods_pic = json_decode($goods_pic,true);
        }
        $goods_thumb = '';
        if(is_array($goods_pic)){
            $goods_pic = array_map(function($pic) {
                return uploadFilePath($pic);
            }, $goods_pic);

            $goods_thumb = $goods_pic[0] ?? '';
            if(!empty($goods_thumb)){
                $goods_thumb = uploadFilePath($goods_thumb);
            }
        }
        $goods_skus = $goods->skus;
        $formattedSkus = [];
        $first_sku = '';
        $first_sku_price = 0;
        $first_sku_currentcy = '';
        $first_sku_thumb_url = '';//小图
        $first_sku_thumb_url_h500 = '';//大图
        if($goods->goods_sku_num > 0){
            foreach ($goods_skus as $key=>$sku) {
                if($key == 0){
                    $first_sku = $sku->spec_key_values;
                    $first_sku_price = $sku->price;
                    $first_sku_currentcy = $sku->currentcy;
                    $first_sku_thumb_url = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                    $first_sku_thumb_url_h500 = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                }
                $formattedSkus[] = [
                    'id' => $sku->id,
                    'sku' => $sku->spec_key_values,
                    'price' => $sku->price,
                    'currentcy' => $sku->currentcy,
                    'thumb_url' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '',
                    'thumb_url_h500' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : ''
                ];
            }
        }

        // 处理关联分类信息
        $platformRelations = $this->formatPlatformRelations($goods->catRelations);

        // 处理用户自定义分类关联信息
        $userCatRelations = $this->formatUserCatRelations($goods->userCatRelations);

        // 用用户自定义分类关联数据替换匹配的平台关联数据
        if ($userCatRelations !== null && $platformRelations !== null) {
            foreach ($userCatRelations as $userRelation) {
                // 在平台关联数据中查找匹配项
                foreach ($platformRelations as $index => $platformRelation) {
                    // 匹配条件：platform_id 且 third_platform_id 值相同
                    if ($platformRelation['platform_id'] == $userRelation['platform_id'] &&
                        $platformRelation['third_platform_id'] == $userRelation['third_platform_id']) {
                        // 将整个数组元素替换为用户自定义关联数据
                        $platformRelations[$index] = $userRelation;
                        break; // 找到匹配项后跳出内层循环
                    }
                }
            }
        }

        // 获取价格调整信息
        $priceAdjustmentInfo = $this->getPriceAdjustmentInfo($goods);

        return [
            'id' => $goods->id,
            'type' => $goods->type,
            'type_name' => $goods->getTypeName(),
            'source_url' => $goods->source_url,
            'mall_id' => $goods->mall_id,
            'goods_id' => $goods->goods_id,
            'goods_name' => $goods->goods_name,
            'goods_thumb' => $goods_thumb,
            'goods_pic' => $goods_pic,
            'images' => $goods_pic, // 添加images字段供前端使用
            'goods_video' => $goods->goods_video,
            'goods_sku_num' => $goods->goods_sku_num,
            'first_sku' => $first_sku,
            'first_sku_price' => $first_sku_price,
            'first_sku_currentcy' => $first_sku_currentcy,
            'first_sku_thumb_url' => $first_sku_thumb_url,
            'first_sku_thumb_url_h500' => $first_sku_thumb_url_h500,
            'formatted_skus' => $formattedSkus,
            'goods_property' => $goods->goods_property,
            'goods_score' => $goods->goods_score,
            'goods_sold_quantity' => $goods->goods_sold_quantity,
            'status' => $goods->status,
            'cat_id' => $goods->cat_id,
            'cat_name' => $goods->frontCategory2?->name ?? '',
            'front_cat_id_2' => $goods->front_cat_id_2,
            'front_cat_desc' => $goods->front_cat_desc,
            'front_cat_2_path_name' => $front_cat_2_path_name,
            'platform_relations' => $platformRelations,
            'is_price_modified' => $goods->is_price_modified,
            'price_adjustment_info' => $priceAdjustmentInfo,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
            'operator_info' => $this->getOperatorInfo($goods),
            'user_sub_id' => $goods->user_sub_id,
        ];
    }

    /**
     * 获取操作者信息
     */
    private function getOperatorInfo($goods): array
    {
        $operatorType = '主账号操作';
        $operatorName = '';

        if ($goods->user_sub_id && $goods->subAccount) {
            $operatorType = '子账号操作';
            $name = $goods->subAccount->name ?? '';
            $phone = $goods->subAccount->phone ?? '';

            if ($name && $phone) {
                $operatorName = $name . '【' . $phone . '】';
            } elseif ($name) {
                $operatorName = $name;
            } elseif ($phone) {
                $operatorName = $phone;
            }
        }

        return [
            'type' => $operatorType,
            'name' => $operatorName,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }

    /**
     * 获取价格调整信息
     */
    private function getPriceAdjustmentInfo($goods): array
    {
        if (!$goods->is_price_modified) {
            return [
                'is_adjusted' => false,
                'display_text' => '-',
                'latest_adjustment_time' => null
            ];
        }

        // 获取最新的价格调整记录
        $latestLog = GoodsPriceAdjustmentLogModel::byGoodsId($goods->id)
            ->orderByModified('desc')
            ->first();

        if (!$latestLog) {
            return [
                'is_adjusted' => false,
                'display_text' => '-',
                'latest_adjustment_time' => null
            ];
        }

        return [
            'is_adjusted' => true,
            'display_text' => '已调整 ' . $latestLog->modified_at,
            'latest_adjustment_time' => $latestLog->modified_at
        ];
    }

    /**
     * 获取用户商品分类关联信息
     */
    private function formatUserCatRelations($userCatRelations): ?array
    {
        if ($userCatRelations->isEmpty()) {
            return null;
        }

        $relations = [];
        foreach ($userCatRelations as $relation) {
            $relationData = [
                'id' => $relation->id,
                'cat_type' => 'user_cat',
                'platform_id' => 1, // 固定为1，表示TEMU平台
                'third_platform_id' => $relation->third_platform_id,
                'cat_third_ids' => [$relation->third_platform_cat_id], // 转换为数组格式，与formatPlatformRelations保持一致
                'third_platform_categories' => null,
            ];

            // 如果是N11平台(third_platform_id = 2)，构建分类详情
            if ($relation->third_platform_id == 2) {
                $relationData['third_platform_categories'] = [[
                    'id' => $relation->third_platform_cat_id,
                    'name' => $relation->third_platform_cat_name,
                    'name_tl' => $relation->third_platform_cat_name_tl,
                    'path_name' => str_ireplace(',', '->', $relation->third_platform_cat_path ?? ''),
                    'path_name_tl' => str_ireplace(',', '->', $relation->third_platform_cat_path_tl ?? ''),
                ]];
            }

            $relations[] = $relationData;
        }

        return $relations;
    }

    /**
     * 格式化平台关联信息
     */
    private function formatPlatformRelations($catRelations): ?array
    {
        if ($catRelations->isEmpty()) {
            return null;
        }

        $relations = [];
        foreach ($catRelations as $relation) {
            $relationData = [
                'id' => $relation->id,
                'cat_type' => 'system_cat',
                'platform_id' => $relation->platform_id,
                'third_platform_id' => $relation->third_platform_id,
                'cat_third_ids' => $relation->cat_third_ids,
                'third_platform_categories' => null,
            ];

            // 如果是N11平台(third_platform_id = 2)，获取分类详情
            if ($relation->third_platform_id == 2) {
                $relationData['third_platform_categories'] = $relation->n11_categories->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'name_tl' => $category->name_tl,
                        'path_name' => str_ireplace(',', '->', $category->path_name ?? ''),
                        'path_name_tl' => str_ireplace(',', '->', $category->path_name_tl ?? ''),
                    ];
                })->toArray();
            }

            $relations[] = $relationData;
        }

        return $relations;
    }
}