<template>
  <transition name="fade">
    <div v-if="visible" class="progress-bar-overlay">
      <div class="progress-container">
        <div class="progress-header">
          <span class="progress-title">{{ title }}</span>
          <span class="progress-text">{{ progressText }}</span>
        </div>
        <el-progress
          :percentage="percentage"
          :status="status"
          :stroke-width="4"
          :show-text="false"
          class="custom-progress"
        />
        <div class="progress-info">
          <span class="current-step">{{ currentStep }}</span>
          <span class="step-info">{{ stepInfo }}</span>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  visible: boolean;
  title?: string;
  percentage: number;
  status?: 'success' | 'exception' | 'warning';
  currentStep?: string;
  stepInfo?: string;
  total?: number;
  current?: number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '处理进度',
  status: undefined,
  currentStep: '',
  stepInfo: '',
  total: 0,
  current: 0
});

const progressText = computed(() => {
  if (props.total > 0 && props.current > 0) {
    return `${props.current}/${props.total}`;
  }
  return `${props.percentage}%`;
});
</script>

<style scoped>
.progress-bar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.95), rgba(103, 194, 58, 0.95));
  backdrop-filter: blur(4px);
  padding: 12px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.progress-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.progress-title {
  flex: 1;
}

.progress-text {
  font-weight: bold;
  margin-left: 10px;
}

.custom-progress {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}

.current-step {
  flex: 1;
}

.step-info {
  opacity: 0.8;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(-100%);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .progress-bar-overlay {
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(103, 194, 58, 0.9));
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-container {
    padding: 0 15px;
  }
  
  .progress-header {
    font-size: 13px;
  }
  
  .progress-info {
    font-size: 11px;
  }
}
</style>