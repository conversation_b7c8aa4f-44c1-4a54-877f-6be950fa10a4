#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单图标生成脚本
用于创建一个基本的应用程序图标
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_simple_icon():
        """创建一个简单的应用程序图标"""
        # 创建不同尺寸的图标
        sizes = [16, 32, 48, 256]
        images = []
        
        for size in sizes:
            # 创建图像
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 绘制背景圆形
            margin = max(1, size // 16)
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=max(1, size//32))
            
            # 绘制文字 "蜂"
            try:
                # 尝试使用系统字体
                font_size = max(8, size // 2)
                font = ImageFont.truetype("msyh.ttc", font_size)  # 微软雅黑
            except:
                try:
                    font = ImageFont.truetype("simsun.ttc", font_size)  # 宋体
                except:
                    font = ImageFont.load_default()
            
            # 计算文字位置
            text = "蜂"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (size - text_width) // 2
            y = (size - text_height) // 2 - bbox[1]
            
            # 绘制文字
            draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
            
            images.append(img)
        
        # 保存为ICO文件
        icon_path = os.path.join(os.path.dirname(__file__), 'app_icon.ico')
        images[0].save(icon_path, format='ICO', sizes=[(img.width, img.height) for img in images])
        
        print(f"✓ 图标文件已创建: {icon_path}")
        return True
        
    if __name__ == "__main__":
        print("正在创建应用程序图标...")
        if create_simple_icon():
            print("图标创建成功！")
        else:
            print("图标创建失败！")
            
except ImportError:
    print("错误: 需要安装 Pillow 库")
    print("请运行: pip install Pillow")
except Exception as e:
    print(f"创建图标时出错: {e}")
    print("\n备选方案:")
    print("1. 使用在线工具创建 .ico 文件")
    print("2. 将现有的 PNG/JPG 图片转换为 ICO 格式")
    print("3. 下载现成的图标文件")
    print("4. 将图标文件命名为 app_icon.ico 并放置到此目录")
