/**
 * 任务处理器
 * 用于处理正常任务的执行逻辑，包括获取分类属性和上传商品
 */
import {
  getN11CategoryAttributes,
  uploadProductToN11,
  processProductAttributes,
  type ProductData,
  type ProductAttribute,
  type N11CategoryResponse
} from './n11ProductApi'
import {
  hasCategoryConfig,
  getCategoryAttributes as getSpecialCategoryAttributes,
  type CategoryAttributeConfig
} from './n11_attr/attr'
import { updateTask, type TaskUpdateParams, getRetryUploadParams, type TaskStartResponse } from './taskApi'
import { handleN11ApiResponse } from './responseHandler'

// 任务响应数据接口
export interface TaskResponse {
  task_over: number
  task_exist: number
  goods_no_cat_relation: number
  id: number
  user_id: number
  task_id: number
  user_account_id: number
  user_goods_id: number
  user_goods_sku_id: number
  goods_id: number
  thumb_url: string
  images: Array<{
    url: string
    order: number
  }>
  images_detail?: string[]
  currentcy_goods: string
  currentcy: string
  price: string
  price_third: string
  price_list_third: string
  spec_key_values: string
  is_skc_gallery: number
  category_id: number
  product_main_id: string
  stock_code: string
  status: number
  goods_info: {
    id: number
    goods_name: string
    goods_property: string
    front_cat_id_2: number
  }
  sku_info: {
    id: number
    sku_id: number
    url: string
    skc_gallery: string
  }
  store_info: {
    id: number
    account_name: string
    account_type: number
    brand: string
    price_rate: number
    shipment_template: string
    quantity: number
    vat_rate: number
    preparing_day: number
    integrator_name: string
    app_key: string
    app_secret: string
  }
  created_at: string
  updated_at: string
  task_count: number
  task_num: number
  // 原有字段（用于兼容）
  goods_name?: string
  store_name?: string
  goods_sku_name?: string
  message?: string
}

// 任务处理结果接口
export interface TaskProcessResult {
  success: boolean
  message: string
  data?: any
  isLastBatch?: boolean
  progress?: {
    current: number
    total: number
    percentage: number
  }
}

// 商品上传结果接口
export interface ProductUploadResult {
  taskDetailId: number
  productName: string
  productImage: string
  price: string
  success: boolean
  message: string
  thirdTaskId?: number
  error?: string
}

// 任务处理器配置
export interface TaskProcessorConfig {
  debugMode?: boolean
  onProgress?: (current: number, total: number) => void
  onProductStart?: (product: ProductUploadResult) => void
  onProductComplete?: (product: ProductUploadResult) => void
  onError?: (error: string) => void
}

/**
 * 获取配置中的调试模式设置
 */
const getDebugMode = (): Promise<boolean> => {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey: 'n11DebugMode'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.warn('获取调试模式配置失败，使用默认值true');
        resolve(true);
        return;
      }
      // 修复：background返回的是url字段，不是value字段
      if (response && response.hasOwnProperty('url')) {
        resolve(response.url);
      } else {
        console.warn('获取调试模式配置失败，使用默认值true:', response);
        resolve(true);
      }
    });
  });
};

/**
 * 处理正常任务执行
 * @param taskResponse 任务响应数据
 * @param onProgress 进度回调函数
 * @returns 处理结果
 */
export const processNormalTask = async (
  taskResponse: TaskResponse,
  onProgress?: (result: ProductUploadResult) => void
): Promise<TaskProcessResult> => {
  let categoryAttributes: N11CategoryResponse | null = null
  let attributes: ProductAttribute[] = []
  let productData: ProductData | null = null
  let uploadResult: any = null
  let productUploadResult: ProductUploadResult

  try {
    console.log('开始处理正常任务:', taskResponse)
    console.log('任务进度信息:', {
      task_count: taskResponse.task_count,
      task_num: taskResponse.task_num,
      current_progress: taskResponse.task_num + 1,
      is_last_batch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1)
    })

    // 1. 检查特殊分类配置并获取分类属性
    const hasSpecialConfig = hasCategoryConfig(taskResponse.category_id)

    if (hasSpecialConfig) {
      console.log(`=== 特殊分类检测到，跳过API获取 ===`)
      console.log(`分类ID: ${taskResponse.category_id}`)
      console.log(`商品名称: ${taskResponse.goods_info.goods_name}`)
      console.log(`品牌信息: ${taskResponse.store_info.brand}`)

      // 直接使用特殊配置，不需要调用API获取分类属性
      categoryAttributes = null // 设置为null，后续处理时会直接使用特殊配置
    } else {
      console.log(`使用API获取分类属性，分类ID: ${taskResponse.category_id}`)
      try {
        categoryAttributes = await getCategoryAttributes(
          taskResponse.category_id,
          taskResponse.store_info.app_key
        )
      } catch (error: any) {
      console.error('获取分类属性失败:', error)
      productUploadResult = {
        taskDetailId: taskResponse.id,
        success: false,
        message: '获取分类属性失败: ' + (error.message || '未知错误'),
        productName: taskResponse.goods_info.goods_name,
        productImage: taskResponse.thumb_url,
        price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
      }

      // 更新任务状态为失败
      try {
        await updateTaskStatus(
          taskResponse.id,
          taskResponse.task_id,
          0,
          'PRODUCT_CREATE',
          'FAIL',
          productUploadResult.message,
          taskResponse.task_num
        )
      } catch (updateError: any) {
        console.error('更新任务状态失败:', updateError)
      }

      if (onProgress) {
        onProgress(productUploadResult)
      }

      // 返回success: true，继续下一个任务
      return {
        success: true,
        message: '任务处理完成（获取分类属性失败）',
        isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
        progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
        }
      }
    }

    // 对于非特殊分类，检查API获取的分类属性是否为空
    if (!hasSpecialConfig && !categoryAttributes) {
      productUploadResult = {
        taskDetailId: taskResponse.id,
        success: false,
        message: '获取分类属性失败，分类属性为空',
        productName: taskResponse.goods_info.goods_name,
        productImage: taskResponse.thumb_url,
        price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
      }

      // 更新任务状态为失败
      try {
        await updateTaskStatus(
          taskResponse.id,
          taskResponse.task_id,
          0,
          'PRODUCT_CREATE',
          'FAIL',
          productUploadResult.message,
          taskResponse.task_num
        )
      } catch (updateError: any) {
        console.error('更新任务状态失败:', updateError)
      }

      if (onProgress) {
        onProgress(productUploadResult)
      }

      // 返回success: true，继续下一个任务
      return {
        success: true,
        message: '任务处理完成（分类属性为空）',
        isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
        progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
      }
    }

        // 2. 处理商品属性
    try {
      if (hasSpecialConfig) {
        console.log(`=== 特殊分类属性处理开始 ===`)

        // 获取特殊配置
        const specialConfig = getSpecialCategoryAttributes(taskResponse.category_id)
        console.log(`特殊配置项数量: ${specialConfig.length}`)
        console.log(`特殊配置详情:`, JSON.stringify(specialConfig, null, 2))

        // 处理特殊配置的属性
        attributes = []
        specialConfig.forEach((configItem, index) => {
          const attribute: ProductAttribute = {
            id: configItem.id,
            valueId: configItem.valueId,
            customValue: configItem.customValue
          }

          // 如果valueId和customValue都为null，则使用品牌信息
          if (configItem.valueId === null && configItem.customValue === null) {
            attribute.customValue = taskResponse.store_info.brand
            console.log(`属性${index + 1} (ID: ${configItem.id}): 使用品牌值 "${taskResponse.store_info.brand}"`)
          } else if (configItem.valueId !== null) {
            console.log(`属性${index + 1} (ID: ${configItem.id}): 使用预定义值ID ${configItem.valueId}`)
          } else if (configItem.customValue !== null) {
            console.log(`属性${index + 1} (ID: ${configItem.id}): 使用自定义值 "${configItem.customValue}"`)
          }

          attributes.push(attribute)
        })

        console.log(`最终处理的属性数量: ${attributes.length}`)
        console.log(`最终属性列表:`, JSON.stringify(attributes, null, 2))
        console.log(`=== 特殊分类属性处理结束 ===`)
      } else {
        // 使用原有的处理逻辑
        console.log(`使用API获取的分类属性进行处理，分类ID: ${taskResponse.category_id}`)
        attributes = processProductAttributes(
          categoryAttributes!.categoryAttributes,
          taskResponse.spec_key_values,
          taskResponse.store_info.brand
        )
      }
    } catch (error: any) {
      console.error('处理商品属性失败:', error)
      productUploadResult = {
        taskDetailId: taskResponse.id,
        success: false,
        message: '处理商品属性失败: ' + (error.message || '未知错误'),
        productName: taskResponse.goods_info.goods_name,
        productImage: taskResponse.thumb_url,
        price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
      }

      // 更新任务状态为失败
      try {
        await updateTaskStatus(
          taskResponse.id,
          taskResponse.task_id,
          0,
          'PRODUCT_CREATE',
          'FAIL',
          productUploadResult.message,
          taskResponse.task_num
        )
      } catch (updateError: any) {
        console.error('更新任务状态失败:', updateError)
      }

      if (onProgress) {
        onProgress(productUploadResult)
      }

      // 返回success: true，继续下一个任务
      return {
        success: true,
        message: '任务处理完成（处理商品属性失败）',
        isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
        progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
      }
    }

    // 3. 构建商品数据
    try {
      productData = buildProductData(taskResponse, attributes)
    } catch (error: any) {
      console.error('构建商品数据失败:', error)
      productUploadResult = {
        taskDetailId: taskResponse.id,
        success: false,
        message: '构建商品数据失败: ' + (error.message || '未知错误'),
        productName: taskResponse.goods_info.goods_name,
        productImage: taskResponse.thumb_url,
        price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
      }

      // 更新任务状态为失败
      try {
        await updateTaskStatus(
          taskResponse.id,
          taskResponse.task_id,
          0,
          'PRODUCT_CREATE',
          'FAIL',
          productUploadResult.message,
          taskResponse.task_num
        )
      } catch (updateError: any) {
        console.error('更新任务状态失败:', updateError)
      }

      if (onProgress) {
        onProgress(productUploadResult)
      }

      // 返回success: true，继续下一个任务
      return {
        success: true,
        message: '任务处理完成（构建商品数据失败）',
        isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
        progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
      }
    }

    // 4. 上传商品到N11
    try {
      // 获取调试模式设置
      const debugMode = await getDebugMode()

      uploadResult = await uploadProductToN11(
        productData,
        taskResponse.store_info.app_key,
        taskResponse.store_info.app_secret,
        taskResponse.store_info.integrator_name,
        taskResponse.id, // 传递任务详情ID用于保存参数
        debugMode // 传递调试模式参数
      )
    } catch (error: any) {
      console.error('上传商品到N11失败:', error)

      // 提取具体的错误信息，优化错误信息的处理
      let errorMessage = '上传商品失败'

      if (error && typeof error === 'object') {
        if (error.errorMessage) {
          errorMessage = error.errorMessage
        } else if (error.exceptionType && error.errorCode) {
          errorMessage = `${error.exceptionType}: ${error.errorMessage || error.errorCode}`
        } else if (error.message) {
          errorMessage = error.message
        } else if (error.data && typeof error.data === 'object') {
          // 处理嵌套的错误数据
          if (error.data.errorMessage) {
            errorMessage = error.data.errorMessage
          } else if (error.data.exceptionType) {
            errorMessage = `${error.data.exceptionType}: ${error.data.errorMessage || error.data.errorCode || ''}`
          }
        }
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      productUploadResult = {
        taskDetailId: taskResponse.id,
        success: false,
        message: errorMessage,
        productName: taskResponse.goods_info.goods_name,
        productImage: taskResponse.thumb_url,
        price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
      }

      // 更新任务状态为失败，传递具体的错误信息
      try {
        await updateTaskStatus(
          taskResponse.id,
          taskResponse.task_id,
          0,
          'PRODUCT_CREATE',
          'FAIL',
          errorMessage,
          taskResponse.task_num
        )
      } catch (updateError: any) {
        console.error('更新任务状态失败:', updateError)
      }

      if (onProgress) {
        onProgress(productUploadResult)
      }

      // 即使上传失败，也返回success: true，因为任务本身已经处理完成
      // 这样可以继续下一个任务，而不是停止整个任务流程
      return {
        success: true, // 改为true，表示任务处理完成（虽然上传失败）
        message: '任务处理完成（上传失败）',
        isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
        progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
      }
    }

    // 5. 处理上传结果
    const isSuccess = !!uploadResult.id
    let resultMessage = ''

    if (isSuccess) {
      // 优先使用uploadResult中的reasons信息（调试模式下会包含"调试模式 - 模拟上传成功"）
      resultMessage = uploadResult.reasons?.[0] || '上传成功'

      // 处理特殊的土耳其语成功消息
      if (resultMessage.includes('sku işlenmeye alındı')) {
        resultMessage = '上传成功'
      }
    } else {
      // 如果上传失败，使用uploadResult中的错误信息
      resultMessage = uploadResult.error || '上传失败'
    }

    productUploadResult = {
      taskDetailId: taskResponse.id,
      success: isSuccess,
      message: resultMessage,
      productName: taskResponse.goods_info.goods_name,
      productImage: taskResponse.thumb_url,
      price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : ''),
      thirdTaskId: uploadResult.id
    }

    // 6. 更新任务状态
    try {
      const thirdTaskId = uploadResult.id || 0
      const thirdType = uploadResult.type || 'PRODUCT_CREATE'
      const thirdStatus = isSuccess ? (uploadResult.status || 'IN_QUEUE') : 'FAIL'

      // 使用具体的错误信息作为third_result
      let thirdResult = ''
      if (isSuccess) {
        thirdResult = uploadResult.reasons ? uploadResult.reasons.join(', ') : '上传成功'

        // 处理特殊的土耳其语成功消息
        if (thirdResult.includes('sku işlenmeye alındı')) {
          thirdResult = '上传成功'
        }
      } else {
        // 失败时使用具体的错误信息
        thirdResult = uploadResult.error || '上传失败'
      }

      await updateTaskStatus(
        taskResponse.id,
        taskResponse.task_id,
        thirdTaskId,
        thirdType,
        thirdStatus,
        thirdResult,
        taskResponse.task_num
      )
    } catch (error: any) {
      console.error('更新任务状态失败:', error)
      // 即使更新状态失败，也不影响整体流程，继续处理
    }

    // 7. 调用进度回调
    if (onProgress) {
      onProgress(productUploadResult)
    }

    // 8. 计算进度信息
    const progress = calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
    const isLastBatch = checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1)

    console.log('任务处理完成，返回结果:', {
      success: true,
      progress,
      isLastBatch,
      task_count: taskResponse.task_count,
      task_num: taskResponse.task_num,
      calculated_current: taskResponse.task_num + 1,
      upload_success: isSuccess,
      upload_message: resultMessage
    })

    return {
      success: true,
      message: '任务处理完成',
      data: uploadResult,
      isLastBatch,
      progress
    }

  } catch (error: any) {
    console.error('处理正常任务失败:', error)

    productUploadResult = {
      taskDetailId: taskResponse.id,
      success: false,
      message: error.message || '处理失败',
      productName: taskResponse.goods_info.goods_name,
      productImage: taskResponse.thumb_url,
      price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
         taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
    }

    // 尝试更新任务状态为失败
    try {
      await updateTaskStatus(
        taskResponse.id,
        taskResponse.task_id,
        0,
        'PRODUCT_CREATE',
        'FAIL',
        productUploadResult.message,
        taskResponse.task_num
      )
    } catch (updateError: any) {
      console.error('更新任务状态失败:', updateError)
    }

    // 调用进度回调
    if (onProgress) {
      onProgress(productUploadResult)
    }

    // 即使发生意外异常，也返回success: true以继续下一个任务
    return {
      success: true,
      message: '任务处理完成（发生异常）',
      isLastBatch: checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1),
      progress: calculateProgress(taskResponse.task_num + 1, taskResponse.task_count)
    }
  }
}

/**
 * 更新任务状态
 * @param taskDetailId 任务详情ID
 * @param taskId 主任务ID
 * @param thirdTaskId 第三方任务ID
 * @param thirdType 第三方类型
 * @param thirdStatus 第三方状态
 * @param thirdResult 第三方结果
 * @param taskNum 当前任务序号
 */
const updateTaskStatus = async (
  taskDetailId: number,
  taskId: number,
  thirdTaskId: number,
  thirdType: string,
  thirdStatus: string,
  thirdResult: string,
  taskNum: number
): Promise<void> => {
  try {
    const params: TaskUpdateParams = {
      task_detail_id: taskDetailId,
      task_id: taskId,
      third_task_id: thirdTaskId,
      third_type: thirdType,
      third_status: thirdStatus,
      third_result: thirdResult,
      task_num: taskNum
    }

    console.log('更新任务状态参数:', params)
    await updateTask(params)
    console.log('任务状态更新成功')
  } catch (error: any) {
    console.error('更新任务状态失败:', error)
    throw error
  }
}

/**
 * 更新任务完成状态
 * @param taskId 主任务ID
 */
export const updateTaskComplete = async (taskId: number): Promise<void> => {
  try {
    const params: TaskUpdateParams = {
      task_id: taskId,
      task_over: 1
    }

    console.log('更新任务完成状态参数:', params)
    await updateTask(params)
    console.log('任务完成状态更新成功')
  } catch (error: any) {
    console.error('更新任务完成状态失败:', error)
    throw error
  }
}

/**
 * 计算进度信息
 * @param current 当前进度
 * @param total 总进度
 * @returns 进度信息
 */
const calculateProgress = (current: number, total: number) => {
  return {
    current: Math.min(current, total), // 确保当前进度不超过总进度
    total,
    percentage: Math.round((Math.min(current, total) / total) * 100)
  }
}

/**
 * 获取分类属性
 * @param categoryId 分类ID
 * @param appKey 应用密钥
 * @returns 分类属性数据
 */
const getCategoryAttributes = async (
  categoryId: number,
  appKey: string
): Promise<N11CategoryResponse | null> => {
  try {
    const response = await getN11CategoryAttributes(categoryId, appKey)

    // N11接口成功返回数据，直接返回
    return response
  } catch (error: any) {
    console.error('获取分类属性失败:', error)
    return null
  }
}

/**
 * 构建商品数据
 * @param taskResponse 任务响应数据
 * @param attributes 商品属性
 * @returns 商品数据
 */
const buildProductData = (
  taskResponse: TaskResponse,
  attributes: ProductAttribute[]
): ProductData => {
  // 处理描述，将逗号替换为HTML换行
  let description = taskResponse.goods_info.goods_property.replace(/,/g, '<br/>')

  // 安全地获取 images_detail，如果不存在则使用空数组
  const images_detail = taskResponse.images_detail ?? []

  // 如果 images_detail 数组不为空，生成图片HTML并拼接到 description
  if (images_detail.length > 0) {
    const imagesHtml = images_detail
      .map(url => `<img src="${url}" style="max-width:800px">`)
      .join('')
    description = `${description}<br/>${imagesHtml}`
  }

  // 货币类型转换逻辑
  const originalCurrency = taskResponse.currentcy?.toUpperCase() || ''
  let convertedCurrency = originalCurrency

  if (originalCurrency === '$') {
    convertedCurrency = 'USD'
  } else if (originalCurrency === '€') {
    convertedCurrency = 'EUR'
  }

  return {
    title: taskResponse.goods_info.goods_name,
    description,
    categoryId: taskResponse.category_id,
    currencyType: convertedCurrency,
    productMainId: taskResponse.product_main_id,
    preparingDay: taskResponse.store_info.preparing_day,
    shipmentTemplate: taskResponse.store_info.shipment_template,
    stockCode: taskResponse.stock_code,
    quantity: taskResponse.store_info.quantity,
    salePrice: parseFloat(taskResponse.price_third),
    listPrice: parseFloat(taskResponse.price_list_third),
    vatRate: taskResponse.store_info.vat_rate,
    images: taskResponse.images,
    attributes
  }
}

/**
 * 检查是否为最后一批任务
 * 注意：不能简单依据task_count值作为最终判断，因为后端每批次返回500条记录
 * 如果task_count是500的整数倍，需要再请求一次确认是否真的完成
 * @param taskCount 当前批次任务总数
 * @param taskNum 已完成任务数
 * @returns 是否可能为最后一批（需要进一步确认）
 */
const checkIsLastBatch = (taskCount: number, taskNum: number): boolean => {
  // 当前批次已完成，但需要通过再次请求API确认是否真的完成
  return taskNum >= taskCount
}

/**
 * 处理单个商品重新上传
 * @param taskDetailId 任务详情ID
 * @param config 处理器配置
 * @returns 上传结果
 */
export const processSingleRetryUpload = async (
  taskDetailId: number,
  config: TaskProcessorConfig = {}
): Promise<ProductUploadResult> => {
  const { onProductStart, onProductComplete, onError } = config

  // 获取调试模式设置
  const debugMode = config.debugMode ?? await getDebugMode()

  try {
    // 获取重新上传参数
    const taskData: TaskStartResponse = await getRetryUploadParams(taskDetailId)

    // 构建商品上传结果对象
    const productResult: ProductUploadResult = {
      taskDetailId: taskDetailId,
      productName: taskData.goods_name || '未知商品',
      productImage: taskData.thumb_url || '',
      price: `${taskData.price_third} ${taskData.currentcy}`,
      success: false,
      message: '正在处理中...'
    }

    // 通知开始处理
    if (onProductStart) {
      onProductStart(productResult)
    }

    // 更新任务状态为处理中
    await updateTask({
      task_detail_id: taskDetailId,
      task_id: taskData.task_id!,
      task_over: 2, // 进行中
      task_num: 0
    })

    // 检查特殊分类配置并获取分类属性
    const hasSpecialConfig = hasCategoryConfig(taskData.category_id!)
    let categoryAttributes = null

    if (hasSpecialConfig) {
      console.log(`=== 重新上传特殊分类检测到，跳过API获取 ===`)
      console.log(`分类ID: ${taskData.category_id}`)
      console.log(`商品名称: ${taskData.goods_name}`)
      console.log(`品牌信息: ${taskData.store_info!.brand}`)

      // 直接使用特殊配置，不需要调用API获取分类属性
      categoryAttributes = null // 设置为null，后续处理时会直接使用特殊配置
    } else {
      console.log(`重新上传使用API获取分类属性，分类ID: ${taskData.category_id}`)
      categoryAttributes = await getCategoryAttributes(
        taskData.category_id!,
        taskData.store_info!.app_key
      )
    }

    // 对于非特殊分类，检查API获取的分类属性是否为空
    if (!hasSpecialConfig && !categoryAttributes) {
      // 分类属性获取失败
      productResult.success = false
      productResult.message = '商品属性获取失败'

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'FAILED',
        third_result: '商品属性获取失败',
        task_over: 1, // 完成
        task_num: 1
      })

      if (onProductComplete) {
        onProductComplete(productResult)
      }

      return productResult
    }

        // 处理商品属性
    let attributes: ProductAttribute[]

    if (hasSpecialConfig) {
      console.log(`=== 重新上传特殊分类属性处理开始 ===`)

      // 获取特殊配置
      const specialConfig = getSpecialCategoryAttributes(taskData.category_id!)
      console.log(`特殊配置项数量: ${specialConfig.length}`)
      console.log(`特殊配置详情:`, JSON.stringify(specialConfig, null, 2))

      // 处理特殊配置的属性
      attributes = []
      specialConfig.forEach((configItem, index) => {
        const attribute: ProductAttribute = {
          id: configItem.id,
          valueId: configItem.valueId,
          customValue: configItem.customValue
        }

        // 如果valueId和customValue都为null，则使用品牌信息
        if (configItem.valueId === null && configItem.customValue === null) {
          attribute.customValue = taskData.store_info!.brand
          console.log(`重新上传属性${index + 1} (ID: ${configItem.id}): 使用品牌值 "${taskData.store_info!.brand}"`)
        } else if (configItem.valueId !== null) {
          console.log(`重新上传属性${index + 1} (ID: ${configItem.id}): 使用预定义值ID ${configItem.valueId}`)
        } else if (configItem.customValue !== null) {
          console.log(`重新上传属性${index + 1} (ID: ${configItem.id}): 使用自定义值 "${configItem.customValue}"`)
        }

        attributes.push(attribute)
      })

      console.log(`重新上传最终处理的属性数量: ${attributes.length}`)
      console.log(`重新上传最终属性列表:`, JSON.stringify(attributes, null, 2))
      console.log(`=== 重新上传特殊分类属性处理结束 ===`)
    } else {
      // 使用原有的处理逻辑
      console.log(`重新上传使用API获取的分类属性进行处理，分类ID: ${taskData.category_id}`)
      attributes = processProductAttributes(
        categoryAttributes!.categoryAttributes,
        taskData.spec_key_values || '',
        taskData.store_info!.brand
      )
    }

    // 货币类型转换逻辑
    const originalCurrency = taskData.currentcy?.toUpperCase() || ''
    let convertedCurrency = originalCurrency

    if (originalCurrency === '$') {
      convertedCurrency = 'USD'
    } else if (originalCurrency === '€') {
      convertedCurrency = 'EUR'
    }

    let description = taskData.goods_info?.goods_property || taskData.goods_name!

      // 安全地获取 images_detail，如果不存在则使用空数组
    const images_detail = taskData.images_detail ?? []

    // 如果 images_detail 数组不为空，生成图片HTML并拼接到 description
    if (images_detail.length > 0) {
      const imagesHtml = images_detail
        .map(url => `<img src="${url}" style="max-width:800px">`)
        .join('')
      description = `${description}<br/>${imagesHtml}`
    }

    // 构建商品数据
    const productData = {
      title: taskData.goods_name!,
      description: description,
      categoryId: taskData.category_id!,
      currencyType: convertedCurrency,
      productMainId: taskData.product_main_id!,
      preparingDay: taskData.store_info!.preparing_day,
      shipmentTemplate: taskData.store_info!.shipment_template,
      stockCode: taskData.stock_code!,
      quantity: taskData.store_info!.quantity,
      salePrice: parseFloat(taskData.price_third!),
      listPrice: parseFloat(taskData.price_list_third!),
      vatRate: taskData.store_info!.vat_rate,
      images: taskData.images || [],
      attributes: attributes
    }

    // 上传商品到N11
    const uploadResult = await uploadProductToN11(
      productData,
      taskData.store_info!.app_key,
      taskData.store_info!.app_secret,
      taskData.store_info!.integrator_name,
      taskDetailId,
      debugMode
    )

    // 处理上传结果
    if (uploadResult.error) {
      // 上传失败
      productResult.success = false
      productResult.message = uploadResult.error
      productResult.error = uploadResult.response

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'FAILED',
        third_result: uploadResult.error,
        task_over: 1, // 完成
        task_num: 1
      })
    } else {
      // 上传成功
      productResult.success = true
      productResult.message = uploadResult.reasons?.[0] || '上传成功'
      productResult.thirdTaskId = uploadResult.id

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: uploadResult.id || 0,
        third_type: 'n11',
        third_status: uploadResult.status || 'SUCCESS',
        third_result: uploadResult.reasons?.[0] || '上传成功',
        task_over: 1, // 完成
        task_num: 1
      })
    }

    // 通知处理完成
    if (onProductComplete) {
      onProductComplete(productResult)
    }

    return productResult

  } catch (error: any) {
    const errorMessage = error.message || '处理失败'

    const productResult: ProductUploadResult = {
      taskDetailId: taskDetailId,
      productName: '未知商品',
      productImage: '',
      price: '',
      success: false,
      message: errorMessage,
      error: error.toString()
    }

    // 更新任务状态为失败
    try {
      await updateTask({
        task_detail_id: taskDetailId,
        task_id: 0, // 无法获取task_id时使用0
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'ERROR',
        third_result: errorMessage,
        task_over: 1, // 完成
        task_num: 1
      })
    } catch (updateError) {
      console.error('更新任务状态失败:', updateError)
    }

    if (onError) {
      onError(errorMessage)
    }

    if (onProductComplete) {
      onProductComplete(productResult)
    }

    return productResult
  }
}

/**
 * 处理批量商品重新上传
 * @param taskDetailIds 任务详情ID数组
 * @param config 处理器配置
 * @returns 上传结果数组
 */
export const processBatchRetryUpload = async (
  taskDetailIds: number[],
  config: TaskProcessorConfig = {}
): Promise<ProductUploadResult[]> => {
  const { onProgress } = config
  const results: ProductUploadResult[] = []

  for (let i = 0; i < taskDetailIds.length; i++) {
    const taskDetailId = taskDetailIds[i]

    // 更新进度
    if (onProgress) {
      onProgress(i, taskDetailIds.length)
    }

    try {
      const result = await processSingleRetryUpload(taskDetailId, config)
      results.push(result)
    } catch (error: any) {
      console.error(`处理任务详情 ${taskDetailId} 失败:`, error)

      const errorResult: ProductUploadResult = {
        taskDetailId: taskDetailId,
        productName: '未知商品',
        productImage: '',
        price: '',
        success: false,
        message: error.message || '处理失败',
        error: error.toString()
      }

      results.push(errorResult)
    }

    // 添加延迟，避免请求过于频繁
    if (i < taskDetailIds.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  // 最终进度更新
  if (onProgress) {
    onProgress(taskDetailIds.length, taskDetailIds.length)
  }

  return results
}
