import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { checkLoginExpired } from './utils/loginState'
import { userInfo, initUserInfoForRouter } from './utils/userStore'

// 导入组件
import Dashboard from './components/Dashboard.vue'
import LoginExpired from './components/LoginExpired.vue'
import VersionUpgrade from './components/VersionUpgrade.vue'
import CardManagement from './components/CardManagement.vue'
import CardUsage from './components/CardUsage.vue'
import AiKeyManagement from './components/AiKeyManagement.vue'
import StoreManagement from './components/StoreManagement.vue'
import GoodsManagement from './components/GoodsManagement.vue'
import GoodsDirectoryManagement from './components/GoodsDirectoryManagement.vue'
import GoodsListByDirectory from './components/GoodsListByDirectory.vue'
import CategoryManagement from './components/CategoryManagement.vue'
import N11CategoryManagement from './components/N11CategoryManagement.vue'
import TaskManagement from './components/TaskManagement.vue'
import SubAccountManagement from './components/SubAccountManagement.vue'

// 临时组件
const AccountSettings = { template: '<div class="temp-component"><h2>账户设置</h2><p>账户设置功能正在开发中...</p></div>' }
const HelpCenter = { template: '<div class="temp-component"><h2>帮助中心</h2><p>帮助中心功能正在开发中...</p></div>' }

const routes: RouteRecordRaw[] = [
  { path: '/', redirect: '/dashboard' },
  { path: '/dashboard', component: Dashboard },
  { path: '/sub-account', component: SubAccountManagement },
  { path: '/card-management', component: CardManagement },
  { path: '/card-usage', component: CardUsage },
  { path: '/ai-key', component: AiKeyManagement },
  { path: '/version-upgrade', component: VersionUpgrade },
  {
    path: '/products',
    component: GoodsManagement,
    children: [
      {
        path: '',
        name: 'GoodsDirectoryList',
        component: GoodsDirectoryManagement
      },
      {
        path: 'directory/:directoryId',
        name: 'GoodsListByDirectory',
        component: GoodsListByDirectory
      },
      {
        path: 'subAccountGoodsList/:directoryId',
        name: 'SubAccountGoodsList',
        component: () => import('./components/SubAccountGoodsList.vue'),
        meta: { title: '商品列表' }
      },
      {
        path: 'subAccountGoods/user/:subAccountId',
        name: 'SubAccountGoodsListByUser',
        component: () => import('./components/SubAccountGoodsList.vue'),
        meta: { title: '子账号商品列表' }
      }
    ]
  },
  { path: '/store', component: StoreManagement },
  { path: '/category', component: CategoryManagement },
  { path: '/n11-category', component: N11CategoryManagement },
  { path: '/tasks', component: TaskManagement },
  { path: '/account', component: AccountSettings },
  { path: '/help', component: HelpCenter },
  { path: '/login-expired', component: LoginExpired },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 用户信息加载等待函数
const waitForUserInfo = async (maxWaitTime = 3000): Promise<boolean> => {
  const startTime = Date.now()

  // 如果用户信息已经加载完成（有手机号说明已登录并加载完成）
  if (userInfo.phone) {
    console.log('用户信息已加载完成，直接返回')
    return true
  }

  // 尝试快速初始化用户信息
  console.log('尝试快速初始化用户信息...')
  const initialized = await initUserInfoForRouter()

  // 如果初始化成功，直接返回
  if (initialized) {
    console.log('用户信息快速初始化成功')
    return true
  }

  // 等待用户信息加载完成或超时
  return new Promise((resolve) => {
    const checkInterval = setInterval(() => {
      const elapsed = Date.now() - startTime

      // 如果用户信息已加载完成
      if (userInfo.phone) {
        clearInterval(checkInterval)
        console.log('用户信息加载完成，耗时:', elapsed, 'ms')
        resolve(true)
        return
      }

      // 如果超时
      if (elapsed >= maxWaitTime) {
        clearInterval(checkInterval)
        console.warn('等待用户信息加载超时，耗时:', elapsed, 'ms')
        resolve(false)
        return
      }
    }, 50) // 每50ms检查一次
  })
}

// 全局前置守卫：检查登录状态和权限
router.beforeEach(async (to, from, next) => {
  // 版本升级页面不需要任何权限检查，直接通过
  if (to.path === '/version-upgrade') {
    next()
    return
  }
  
  // 如果登录已失效且不是前往登录失效页面，则重定向到登录失效页面
  if (checkLoginExpired() && to.path !== '/login-expired') {
    next('/login-expired')
    return
  }

  // 需要VIP权限的路由列表（除了卡密激活页面）
  const vipRequiredRoutes = ['/dashboard', '/ai-key', '/store', '/category', '/n11-category', '/products', '/tasks', '/account', '/help']

  // 检查VIP身份验证（在卡密管理权限检查之前）
  if (vipRequiredRoutes.includes(to.path)) {
    console.log('检查VIP权限，当前用户信息:', { phone: userInfo.phone, isVip: userInfo.isVip })

    // 等待用户信息加载完成
    const userInfoLoaded = await waitForUserInfo()

    if (!userInfoLoaded) {
      console.warn('用户信息加载超时，重定向到卡密激活页面')
      next('/card-usage')
      return
    }

    // 用户信息加载完成后，检查VIP身份
    if (!userInfo.isVip) {
      console.warn('用户不是VIP，重定向到卡密激活页面')
      next('/card-usage')
      return
    }

    console.log('VIP权限检查通过')
  }

  // 检查卡密管理权限
  if (to.path === '/card-management') {
    // 确保用户信息已加载
    await waitForUserInfo()

    if (!userInfo.isCardAdmin) {
      console.warn('用户没有卡密管理权限，重定向到控制面板')
      next('/dashboard')
      return
    }
  }

  next()
})

// 添加全局样式
const style = document.createElement('style')
style.textContent = `
  .temp-component {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .temp-component h2 {
    margin-top: 0;
    color: #333;
    font-size: 1.5rem;
    margin-bottom: 15px;
  }
  .temp-component p {
    color: #666;
    font-size: 1rem;
  }
`
document.head.appendChild(style)

export default router
