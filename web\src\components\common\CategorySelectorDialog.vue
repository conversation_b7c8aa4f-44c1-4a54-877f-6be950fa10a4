<template>
  <el-dialog
    v-model="visible"
    title="修改AI分类结果"
    width="900px"
    :close-on-click-modal="false"
    :z-index="3000"
    append-to-body
    class="category-selector-dialog"
    @close="handleClose"
  >
    <div class="category-selector-content">
      <!-- 商品模式：当前商品的TEMU分类信息 -->
      <div v-if="mode === 'goods' && isAdmin && temuCategory && false" class="current-category-info">
        <h3>当前商品的TEMU分类</h3>
        <div class="category-info">
          <span class="category-name">{{ temuCategory.cat_name }}</span>
          <span class="category-path">{{ temuCategory.front_cat_2_path_name }}</span>
        </div>
      </div>

      <!-- 商品模式：已关联的分类信息 -->
      <div v-if="mode === 'goods' && relatedCategories && relatedCategories.length > 0" class="linked-categories-info">
        <h3>当前AI识别的分类</h3>
        <div class="linked-categories-list">
          <el-table :data="relatedCategories" stripe border size="small">
            <el-table-column label="分类名称(翻译)" min-width="250" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="linked-category-name">
                  {{ row.name }}
                  <template v-if="row.path_name">
                    <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
                  </template>
                </span>
              </template>
            </el-table-column>
            <el-table-column label="分类名称" min-width="250" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="linked-category-name-tl">
                  {{ row.name_tl }}
                  <template v-if="row.path_name_tl">
                    <span class="path-display">【{{ row.path_name_tl.replace(/,/g, '->') }}】</span>
                  </template>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 任务模式：当前任务的分类信息 -->
      <div v-if="mode === 'task' && currentCategory" class="current-category-info">
        <h3>当前AI识别的分类</h3>
        <div class="category-info">
          <span class="category-name">{{ currentCategory.path_name || currentCategory.name || '未知分类' }}</span>
          <span class="category-path" v-if="currentCategory.path_name_tl">{{ currentCategory.path_name_tl }}</span>
        </div>
      </div>

      <!-- 选择平台（固定显示N11） -->
      <div class="platform-section">
        <el-form label-width="100px">
          <el-form-item label="选择平台">
            <el-radio-group v-model="selectedPlatformId" disabled>
              <el-radio :label="2">N11</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 检索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="分类名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入分类名称"
              clearable
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              检索
            </el-button>
            <el-button type="success" @click="handleConfirm" :disabled="selectedCategories.length === 0">
              确认修改AI分类结果
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- N11分类表格 -->
      <div class="table-section">
        <el-table
          :data="n11CategoryList"
          v-loading="loading"
          stripe
          border
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-expand-all="false"
          ref="tableRef"
          @select="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :selectable="isSelectable">
            <template #header>
              <span style="color: #909399; font-size: 12px;">选择</span>
            </template>
          </el-table-column>
          <el-table-column label="ID" width="120" align="right">
            <template #default="{ row }">
              <!-- 强制显示为空，不显示具体的ID值 -->
            </template>
          </el-table-column>
          <el-table-column label="分类名称(翻译)" prop="name" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="category-name" :class="{ [`level-${row.level}`]: row.level }">
                {{ row.name }}
                <template v-if="row.path_name">
                  <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
                </template>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="分类名称" prop="name_tl" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="category-name" :class="{ [`level-${row.level}`]: row.level }">
                {{ row.name_tl }}
                <template v-if="row.path_name_tl">
                  <span class="path-display">【{{ row.path_name_tl.replace(/,/g, '->') }}】</span>
                </template>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="层级" prop="level" width="60" />
          <el-table-column label="末级" width="60">
            <template #default="{ row }">
              <el-tag :type="row.is_leaf ? 'success' : 'info'">
                {{ row.is_leaf ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="selectedCategories.length === 0"
        >
          确认修改AI分类结果
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElTable } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { userInfo } from '../../utils/userStore'
import { getN11CategoryList, type ThirdPartyCategory } from '../../utils/thirdPartyApi'
import {
  saveGoodsCatRelation,
  type SaveGoodsCatRelationParams
} from '../../utils/goodsCatRelationApi'

// Props定义
interface CurrentTemuCategory {
  cat_id: number
  cat_name: string
  front_cat_2_path_name: string
}

interface RelatedCategory {
  id: number
  name: string
  name_tl: string
  path_name: string
  path_name_tl: string
}

interface CurrentCategory {
  id: number
  name: string
  name_tl?: string
  path_name?: string
  path_name_tl?: string
}

const props = defineProps<{
  modelValue: boolean
  
  // 当前分类信息（任务模式）
  currentCategory?: CurrentCategory | null
  
  // TEMU分类信息（商品模式）
  temuCategory?: CurrentTemuCategory
  
  // 已关联分类列表（商品模式）
  relatedCategories?: RelatedCategory[]
  
  // 模式配置
  mode: 'goods' | 'task'
  
  // 业务ID
  businessId: number
  
  // 是否显示管理员专属内容
  showAdminContent?: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: {
    categoryId: number
    category: ThirdPartyCategory
    mode: 'goods' | 'task'
    businessId: number
  }]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  // 如果明确传入了showAdminContent，使用该值；否则使用用户信息判断
  if (props.showAdminContent !== undefined) {
    return props.showAdminContent
  }
  return userInfo.isAdmin === true
})

const loading = ref(false)
const submitting = ref(false)
const selectedPlatformId = ref(2) // 固定为N11平台
const n11CategoryList = ref<ThirdPartyCategory[]>([])
const selectedCategories = ref<ThirdPartyCategory[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取N11分类列表
const getN11Categories = async () => {
  loading.value = true
  try {
    const response = await getN11CategoryList({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...(searchForm.name && { name: searchForm.name })
    })

    n11CategoryList.value = response.list || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取N11分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 判断行是否可选择（只能选择子节点）
const isSelectable = (row: ThirdPartyCategory) => {
  return row.is_leaf === true || (row.is_leaf as any) === 1
}

// 处理选择变化
const handleSelectionChange = (selection: ThirdPartyCategory[], row: ThirdPartyCategory) => {
  // 只处理末级分类的选择
  if (row.is_leaf !== true && (row.is_leaf as any) !== 1) {
    return
  }

  // 限制只能选择一项（单选效果）
  if (tableRef.value) {
    tableRef.value.clearSelection()
    if (selection.includes(row)) {
      tableRef.value.toggleRowSelection(row, true)
      selectedCategories.value = [row]
    } else {
      selectedCategories.value = []
    }
  }
}

// 检索
const handleSearch = () => {
  pagination.currentPage = 1
  selectedCategories.value = []
  getN11Categories()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getN11Categories()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getN11Categories()
}

// 确认选择
const handleConfirm = async () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请选择要关联的分类')
    return
  }

  const selectedCategory = selectedCategories.value[0]

  // 根据模式进行不同的验证
  if (props.mode === 'goods') {
    // 商品模式：检查是否选择了与当前已关联相同的分类
    const isAlreadyLinked = props.relatedCategories?.some(
      category => category.id === selectedCategory.id
    )

    if (isAlreadyLinked) {
      ElMessage.info('该分类已经关联，无需重复关联')
      return
    }
  } else if (props.mode === 'task') {
    // 任务模式：检查是否选择了与当前相同的分类
    if (props.currentCategory && props.currentCategory.id === selectedCategory.id) {
      ElMessage.info('该分类是当前识别的分类，无需重复关联')
      return
    }
  }

  try {
    const confirmMessage = props.mode === 'goods' 
      ? `确认将AI识别的分类关联到N11分类"${selectedCategory.name}"吗？`
      : `确认将AI识别的分类关联到N11分类"${selectedCategory.name}"吗？`

    await ElMessageBox.confirm(
      confirmMessage,
      '确认修改AI分类结果',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        appendTo: 'body'
      }
    )

    submitting.value = true

    if (props.mode === 'goods') {
      // 商品模式：直接调用保存接口
      if (!props.temuCategory) {
        throw new Error('缺少TEMU分类信息')
      }

      const saveParams: SaveGoodsCatRelationParams = {
        goods_id: props.businessId,
        temu_cat_id: props.temuCategory.cat_id,
        third_platform_id: 2, // N11平台
        third_platform_cat_id: selectedCategory.id,
        third_platform_cat_name: selectedCategory.name,
        third_platform_cat_name_tl: selectedCategory.name_tl || '',
        third_platform_cat_path: selectedCategory.path_name || '',
        third_platform_cat_path_tl: selectedCategory.path_name_tl || ''
      }

      await saveGoodsCatRelation(saveParams)
    }

    // 统一发送确认事件
    emit('confirm', {
      categoryId: selectedCategory.id,
      category: selectedCategory,
      mode: props.mode,
      businessId: props.businessId
    })

    handleClose()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置数据
  selectedCategories.value = []
  searchForm.name = ''
  pagination.currentPage = 1
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      getN11Categories()
    })
  }
})
</script>

<style scoped>
.category-selector-dialog {
  z-index: 3000 !important;
}

.category-selector-content {
  max-height: 70vh;
  overflow-y: auto;
}

.current-category-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.current-category-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.category-id {
  background-color: #f0f2f5;
  padding: 2px 8px;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  font-family: monospace;
}

.category-name {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.category-path {
  color: #666;
  font-size: 14px;
}

.linked-categories-info {
  background: #fff9e6;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #ffd666;
}

.linked-categories-info h3 {
  margin: 0 0 15px 0;
  color: #e6a23c;
  font-size: 16px;
}

.linked-categories-list {
  max-height: 200px;
  overflow-y: auto;
}

.linked-category-name {
  font-weight: bold;
  color: #409eff;
}

.linked-category-name-tl {
  font-weight: bold;
  color: #67c23a;
}

.platform-section,
.search-section {
  background: #fff;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

/* 分类名称样式 */
.category-name.level-1 {
  font-weight: bold;
  color: #409eff;
}

.category-name.level-2 {
  color: #67c23a;
}

.category-name.level-3 {
  color: #e6a23c;
}

.category-name.level-4 {
  color: #f56c6c;
}

.path-display {
  color: #999;
  margin-left: 10px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>

<style>
/* 全局样式：确保MessageBox的z-index高于对话框 */
.el-message-box__wrapper {
  z-index: 3001 !important;
}

.el-overlay {
  z-index: 3001 !important;
}
</style>