<template>
  <div class="task-management">
    <!-- 任务列表视图 -->
    <div v-if="currentView === 'list'">
      <div class="page-header">
        <h2>任务列表</h2>
      </div>

      <!-- 搜索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="完成状态" class="status-item">
            <el-select v-model="searchForm.task_over" placeholder="请选择完成状态" clearable>
              <el-option label="未完成" :value="0" />
              <el-option label="已完成" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :shortcuts="dateShortcuts"
              @change="handleDateRangeChange" />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.has_rejected" style="margin-right: 20px;">
              审核未通过
            </el-checkbox>
            <el-button type="primary" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset" style="margin-right: 20px;">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table :data="taskList" v-loading="loading" stripe border>
          <el-table-column label="序号" type="index" width="80" :index="getIndex" />
          <el-table-column label="店铺名称" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <div>
                <div>{{ row.store_name }}</div>
                <div class="task-id-info">任务ID-{{ row.id }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品范围" min-width="120">
            <template #default="{ row }">
              <div>目录：{{ row.directory_name || '未知目录' }}</div>
              <div>{{ getProductRange(row) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="商品数量" width="120">
            <template #default="{ row }">
              <div>商品数量：{{ row.goods_count || 0 }}</div>
              <div>SKU数量：{{ row.sku_count || 0 }}</div>
              <div v-if="row.currentcy" class="currency-info">发布货币：{{ row.currentcy }}</div>
            </template>
          </el-table-column>
          <el-table-column label="价格范围" width="120">
            <template #default="{ row }">
              <div>{{ getPriceRangeDisplay(row) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="价格调整" width="140">
            <template #default="{ row }">
              <div>{{ getPriceAdjustmentDisplay(row) }}</div>
              <div v-if="row.exchange_rate_currency" class="exchange-currency-info">{{ row.exchange_rate_currency }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="任务进度" width="180">
            <template #default="{ row }">
              <div>已完成：{{ row.task_num || 0 }}</div>
              <div>总数量：{{ row.is_selected === 0 && (row.task_over === 0 || row.task_count === 0) ? '--' :
                (row.task_count || 0) }}</div>
              <div v-if="row.rejected_count > 0" class="rejected-count">审核未通过：{{ row.rejected_count || 0 }}</div>
            </template>
          </el-table-column>
          <el-table-column label="时间范围" min-width="180">
            <template #default="{ row }">
              <span>{{ formatTimeRangeDisplay(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最近上传" width="140">
            <template #default="{ row }">
              <span>{{ getLatestUploadTime(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="完成状态" width="120">
            <template #default="{ row }">
              <div class="status-container">
                <div class="ai-status-row">
                  <el-tag :type="getAiStatusTagType(row.goods_ai_name_status, row.task_over)" size="small">
                    {{ getAiStatusName(row.goods_ai_name_status, row.task_over) }}
                  </el-tag>
                </div>
                <div class="task-status-row">
                  <el-tag :type="getStatusTagType(row.task_over)" size="small">
                    任务{{ getStatusName(row.task_over) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="时间信息" width="180">
            <template #default="{ row }">
              <div>创建：{{ row.created_at }}</div>
              <div>更新：{{ row.updated_at }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <!-- 如果任务未完成且AI标题也未完成，只显示AI改写标题按钮 -->
              <el-button v-if="row.goods_ai_name_status === 0" type="warning" size="small"
                @click="handleAiRewriteTitle(row)" :loading="aiRewritingTaskId === row.id">
                AI改写标题
              </el-button>
              <!-- 如果任务未完成且AI标题已完成，显示开始按钮 -->
              <el-button v-if="row.task_over === 0 && row.goods_ai_name_status === 1" type="primary" size="small"
                @click="handleStartTask(row)" :loading="startingTaskId === row.id">
                开始
              </el-button>
              <!-- 如果任务已完成，显示详情按钮 -->
              <el-button v-if="row.task_over === 1 && row.goods_ai_name_status === 1" type="success" size="small"
                @click="handleViewDetails(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>

      <!-- 任务加载对话框 -->
      <TaskLoadingDialog v-model:visible="loadingDialogVisible" :title="loadingTitle" :message="loadingMessage" />

      <!-- 任务进度对话框 -->
      <TaskProgressDialog v-model:visible="progressDialogVisible" :dialog-title="getProgressDialogTitle()"
        :task-title="currentTask?.store_name || ''" :current-progress="taskProgress.currentProgress"
        :total-progress="taskProgress.totalProgress" :current-product="taskProgress.currentProduct"
        :is-processing="taskProgress.isProcessing" :is-completed="taskProgress.isCompleted"
        :has-error="taskProgress.hasError" :error-message="taskProgress.errorMessage"
        :processing-message="taskProgress.processingMessage" :process-history="taskProgress.processHistory"
        @close="handleProgressDialogClose" @cancel="handleProgressDialogCancel" />
    </div>

    <!-- 任务详情视图 -->
    <TaskDetail v-if="currentView === 'detail' && currentTaskId" :task-id="currentTaskId" @go-back="handleBackToList" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { getTaskList, startTask, type Task, type TaskListParams, type TaskStartResponse } from '../utils/taskApi'
import { processNormalTask, updateTaskComplete, type TaskResponse, type ProductUploadResult } from '../utils/taskProcessor'
import { forceRefreshUserInfo } from '../utils/userStore'
import {
  generateTaskDetails,
  getNextAiTask,
  updateAiResult,
  getRandomAiKey,
  getAiProgress,
  completeAiTask,
  callDeepSeekApi,
  constructTitlePrompt,
  constructPropertyPrompt,
  extractSkuValues,
  prependBrand,
  type TaskNewGenerateDetailsParams,
  type TaskNewGenerateDetailsResponse,
  type TaskNewNextAiTaskResponse,
  type TaskNewAiProgressResponse,
  type TaskDetailForAi
} from '../utils/aiRewriteApi'
import TaskLoadingDialog from './TaskLoadingDialog.vue'
import TaskProgressDialog from './TaskProgressDialog.vue'
import TaskDetail from './TaskDetail.vue'

// 响应式数据
const loading = ref(false)
const taskList = ref<Task[]>([])
const dateRange = ref<string[]>([])
const startingTaskId = ref<number | null>(null)
const aiRewritingTaskId = ref<number | null>(null)

// 对话框状态
const loadingDialogVisible = ref(false)
const progressDialogVisible = ref(false)
const loadingTitle = ref('任务数据加载中')
const loadingMessage = ref('正在处理任务数据，请稍候...')

// 当前任务和商品信息
const currentTask = ref<Task | null>(null)
const currentStoreBrand = ref<string>('')
const aiRewriteCancelled = ref<boolean>(false)
const isAiRewriteTask = ref<boolean>(false)

// 任务进度相关数据
const taskProgress = reactive({
  currentProgress: 0,
  totalProgress: 0,
  isProcessing: false,
  isCompleted: false,
  hasError: false,
  errorMessage: '',
  processingMessage: '正在处理商品...',
  currentProduct: null as ProductUploadResult | null,
  processHistory: [] as ProductUploadResult[]
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 搜索表单
const searchForm = reactive<{
  task_over: number | null | undefined
  day_start: string | undefined
  day_end: string | undefined
  has_rejected: boolean
}>({
  task_over: undefined,
  day_start: undefined,
  day_end: undefined,
  has_rejected: false
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取状态名称
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '已完成',
    0: '未完成'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'warning'
}

// 获取AI状态名称（兼容已完成任务）
const getAiStatusName = (aiStatus: number, taskOver: number) => {
  // 如果任务已完成，强制显示AI标题已完成
  /* if (taskOver === 1) {
    return 'AI标题已完成'
  } */

  const statusMap: Record<number, string> = {
    1: 'AI标题已完成',
    0: 'AI标题未完成'
  }
  return statusMap[aiStatus] || 'AI状态未知'
}

// 获取AI状态标签类型（兼容已完成任务）
const getAiStatusTagType = (aiStatus: number, taskOver: number) => {
  // 如果任务已完成，强制显示为成功状态
  /* if (taskOver === 1) {
    return 'success'
  } */

  return aiStatus === 1 ? 'success' : 'danger'
}

// 获取商品范围显示
const getProductRange = (row: Task) => {
  if (row.is_selected === 1) {
    return '指定商品'
  } else {
    // 如果time_range是all，显示"全部"
    if (row.time_range === 'all') {
      return '全部'
    } else {
      return '指定时间范围'
    }
  }
}

// 获取排序方式显示
const getSortOrderDisplay = (row: Task) => {
  if (row.is_selected === 0) {
    const sortOrderMap: Record<string, string> = {
      'desc': '降序',
      'asc': '升序'
    }
    return sortOrderMap[row.sort_order] || '未知'
  }
  return '--'
}

// 获取最近上传时间
const getLatestUploadTime = (row: Task) => {
  return row.latest_time || '--'
}

// 格式化价格数字，去除不必要的小数位和尾随零（处理字符串类型）
const formatPriceNumber = (price: string | number | undefined | null): string => {
  if (price === undefined || price === null || price === '') {
    return '0'
  }

  // 转换为数字
  const numPrice = typeof price === 'string' ? parseFloat(price) : price

  // 如果转换失败或为NaN，返回0
  if (isNaN(numPrice)) {
    return '0'
  }

  // 如果是整数或者小数部分为0，直接返回整数部分
  if (numPrice % 1 === 0) {
    return Math.floor(numPrice).toString()
  }

  // 去除尾随零
  return numPrice.toString().replace(/\.?0+$/, '')
}

// 获取价格范围显示
const getPriceRangeDisplay = (row: Task) => {
  const minPrice = row.price_min
  const maxPrice = row.price_max

  // 转换为数字进行判断
  const minNum = typeof minPrice === 'string' ? parseFloat(minPrice || '0') : (minPrice || 0)
  const maxNum = typeof maxPrice === 'string' ? parseFloat(maxPrice || '0') : (maxPrice || 0)

  // 如果两个价格都是0或都不存在，显示 "--"
  if (minNum === 0 && maxNum === 0) {
    return '--'
  }

  const formattedMin = formatPriceNumber(minPrice)
  const formattedMax = formatPriceNumber(maxPrice)

  return `${formattedMin} - ${formattedMax}`
}

// 获取价格调整显示
const getPriceAdjustmentDisplay = (row: Task) => {
  const formattedRate = formatPriceNumber(row.price_rate)
  const formattedAdd = formatPriceNumber(row.price_add)
  const formattedSubtract = formatPriceNumber(row.price_subtract)

  return `X ${formattedRate} + ${formattedAdd} - ${formattedSubtract}`
}

// 格式化时间范围显示
const formatTimeRangeDisplay = (row: Task) => {
  if (row.is_selected === 1) {
    return '----'
  } else {
    // 根据time_range字段显示不同内容
    switch (row.time_range) {
      case 'all':
        return '全部'
      case 'today':
        return '今天'
      case 'yesterday':
        return '昨天'
      case 'lastweek':
        return '最近一周'
      case 'custom':
        if (row.day_start && row.day_end) {
          if (row.day_start === row.day_end) {
            return row.day_start
          } else {
            return `${row.day_start} 至 ${row.day_end}`
          }
        }
        return '自定义'
      default:
        return '未知'
    }
  }
}

// 开始任务
const handleStartTask = async (row: Task) => {
  try {
    // 设置当前任务
    currentTask.value = row
    isAiRewriteTask.value = false // 标记为普通任务
    startingTaskId.value = row.id

    // 显示加载对话框
    loadingTitle.value = '任务数据加载中'
    loadingMessage.value = `正在处理店铺 "${row.store_name}" 的任务，请稍候...`
    loadingDialogVisible.value = true

    // 调用开始任务API
    const response: TaskStartResponse = await startTask(row.id)

    // 隐藏加载对话框
    loadingDialogVisible.value = false

    // 统一在进度组件中处理所有情况
    await handleTaskResponse(response, row)

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消了操作
      ElMessage.info('已取消任务开始操作')
    } else {
      console.error('开始任务失败:', error)
      loadingDialogVisible.value = false
      ElMessage.error('开始任务失败，请重试')
    }
  } finally {
    startingTaskId.value = null
  }
}

// 统一处理任务响应
const handleTaskResponse = async (response: TaskStartResponse, row: Task) => {
  // 获取正确的任务总数，优先使用响应中的task_count，其次使用row中的task_count
  const totalTaskCount = response.task_count || row.task_count || 0
  console.log("475行 handleTaskResponse 中的 currentTask.value===", currentTask.value);
  // 初始化进度数据
  Object.assign(taskProgress, {
    currentProgress: 0,
    totalProgress: totalTaskCount,
    isProcessing: false,
    isCompleted: false,
    hasError: false,
    errorMessage: '',
    processingMessage: '',
    currentProduct: null,
    processHistory: []
  })

  // 显示进度对话框
  progressDialogVisible.value = true

  if (response.task_over === 1) {
    // 任务已完成
    taskProgress.isCompleted = true
    taskProgress.processingMessage = `任务"${row.store_name}"已全部完成！共处理了 ${totalTaskCount} 个商品。`
    taskProgress.currentProgress = totalTaskCount

    // 3秒后自动刷新任务列表
    setTimeout(() => {
      loadTaskList()
      handleProgressDialogClose()
    }, 3000)

  } else if (response.goods_no_cat_relation === 1) {
    //这种情况实际操作中已经不存在了 但是保留该判断
    // 遇到分类未关联的商品，记录错误但继续下一个
    const errorProductResult: ProductUploadResult = {
      taskDetailId: response.id || 0,
      success: false,
      message: '商品分类未关联，跳过此商品',
      productName: response.goods_name || '未知商品',
      productImage: response.thumb_url || '',
      price: response.price_third ? `${response.price_third} ${response.currentcy}` : ''
    }

    taskProgress.currentProduct = errorProductResult
    taskProgress.processHistory.push({ ...errorProductResult })

    // 更新总进度数（如果服务器返回了更准确的数据）
    if (response.task_count && response.task_count > taskProgress.totalProgress) {
      taskProgress.totalProgress = response.task_count
    }

    // 使用服务器返回的task_num + 1作为当前进度
    if (response.task_num !== undefined) {
      taskProgress.currentProgress = response.task_num + 1
    } else {
      taskProgress.currentProgress = Math.min(
        taskProgress.currentProgress + 1,
        taskProgress.totalProgress
      )
    }
    taskProgress.processingMessage = '分类未关联，跳过此商品，继续下一个...'

    // 继续下一个任务
    setTimeout(() => {
      handleContinueNextBatch()
    }, 2000)
  } else if (response.task_over === 0 && response.task_exist === 0 && response.goods_no_cat_relation === 0) {
    // 继续处理下一批正常任务
    // 确保response包含必要的字段
    if (response.id && response.task_id) {
      await handleNormalTask(response as TaskResponse, currentTask.value.store_name)
    } else {
      // 数据不完整，记录错误但继续下一个
      const errorProductResult: ProductUploadResult = {
        taskDetailId: 0,
        success: false,
        message: '任务响应数据不完整，跳过此商品',
        productName: '数据不完整',
        productImage: '',
        price: ''
      }

      taskProgress.currentProduct = errorProductResult
      taskProgress.processHistory.push({ ...errorProductResult })
      taskProgress.processingMessage = '数据不完整，跳过此商品，继续下一个...'

      // 继续下一个任务
      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    }
  } else if (response.task_exist === 1) {
    //这种情况实际操作中已经不存在了 但是保留该判断
    // 又遇到已存在的商品，立即显示并跳过
    const productResult: ProductUploadResult = {
      taskDetailId: response.id || 0,
      success: false,
      message: '商品已存在，跳过处理',
      productName: response.goods_name || '未知商品',
      productImage: response.thumb_url || '',
      price: response.price && response.price_third ?
        `原价: ${response.price} ${response.currentcy_goods || ''} | 售价: ${response.price_third} ${response.currentcy || ''}` :
        (response.price_third ? `${response.price_third} ${response.currentcy || ''}` :
          response.price ? `${response.price} ${response.currentcy_goods || ''}` : '')
    }

    // 立即显示当前处理的商品
    taskProgress.currentProduct = productResult
    taskProgress.processHistory.push({ ...productResult })

    // 更新总进度数（如果服务器返回了更准确的数据）
    if (response.task_count && response.task_count > taskProgress.totalProgress) {
      taskProgress.totalProgress = response.task_count
    }

    // 使用服务器返回的task_num + 1作为当前进度
    if (response.task_num !== undefined) {
      taskProgress.currentProgress = response.task_num + 1
    } else {
      taskProgress.currentProgress = Math.min(
        taskProgress.currentProgress + 1,
        taskProgress.totalProgress
      )
    }
    taskProgress.processingMessage = '商品已存在，正在继续处理下一个...'

    // 继续下一个
    setTimeout(() => {
      handleContinueNextBatch()
    }, 2000)
  }
}

// 检查是否为最后一批任务
const checkIsLastBatch = (taskCount: number, taskNum: number): boolean => {
  return taskNum >= taskCount
}

// 处理正常任务
const handleNormalTask = async (taskResponse: TaskResponse, storeName: string) => {
  try {
    // 更新进度状态
    taskProgress.isProcessing = true
    taskProgress.processingMessage = '正在获取分类属性...'
    // 使用任务响应中的当前进度，但要确保从1开始显示
    taskProgress.currentProgress = taskResponse.task_num + 1

    // 立即显示当前处理的商品信息
    const currentProductInfo: ProductUploadResult = {
      taskDetailId: taskResponse.id,
      success: false, // 初始状态为false，处理完成后会更新
      message: '正在处理中...',
      productName: taskResponse.goods_name,
      productImage: taskResponse.thumb_url,
      price: taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
          taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
    }
    taskProgress.currentProduct = currentProductInfo

    // 处理任务
    const result = await processNormalTask(taskResponse, (productResult: ProductUploadResult) => {
      // 更新当前商品信息
      taskProgress.currentProduct = productResult

      // 添加到处理历史
      taskProgress.processHistory.push({ ...productResult })

      // 更新处理消息
      if (productResult.success) {
        taskProgress.processingMessage = '商品上传成功，准备处理下一个...'
      } else {
        taskProgress.processingMessage = '商品上传失败，准备处理下一个...'
      }
    })

    // 处理完成后更新进度
    if (result.progress) {
      taskProgress.currentProgress = result.progress.current
    }

    // 处理完成
    taskProgress.isProcessing = false

    // 无论成功还是失败，都要检查是否继续下一个任务
    if (result.isLastBatch) {
      // 当前批次已完成，但需要再次请求确认是否真的完成
      taskProgress.processingMessage = '当前批次已完成，正在检查是否还有更多任务...'
      setTimeout(() => {
        handleContinueNextBatch()
      }, 1000)
    } else {
      // 继续下一批任务，无论当前任务成功还是失败
      if (result.success) {
        taskProgress.processingMessage = '当前商品处理成功，正在继续下一个...'
      } else {
        taskProgress.processingMessage = '当前商品处理失败，正在继续下一个...'
      }

      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    }

  } catch (error: any) {
    console.error('处理正常任务失败:', error)

    // 检查是否已取消任务，如果是则直接返回
    if (aiRewriteCancelled.value || !currentTask.value || taskProgress.hasError) {
      return
    }

    // 判断错误类型，优化错误信息显示
    let userFriendlyMessage = '商品处理失败'
    if (error.message) {
      if (error.message.includes('Cannot read properties') ||
          error.message.includes('TypeError') ||
          error.message.includes('null')) {
        userFriendlyMessage = '数据异常，跳过此商品'
      } else if (error.message.includes('网络') || error.message.includes('timeout')) {
        userFriendlyMessage = '网络连接异常'
      } else {
        userFriendlyMessage = error.message || '商品处理失败'
      }
    }

    // 创建失败的商品结果
    const productUploadResult: ProductUploadResult = {
      taskDetailId: taskResponse?.id || 0,
      success: false,
      message: userFriendlyMessage,
      productName: taskResponse?.goods_info?.goods_name || '未知商品',
      productImage: taskResponse?.thumb_url || '',
      price: taskResponse && taskResponse.price && taskResponse.price_third ?
        `原价: ${taskResponse.price} ${taskResponse.currentcy_goods || ''} | 售价: ${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
        (taskResponse && taskResponse.price_third ? `${taskResponse.price_third} ${taskResponse.currentcy || ''}` :
          taskResponse && taskResponse.price ? `${taskResponse.price} ${taskResponse.currentcy_goods || ''}` : '')
    }

    // 添加到处理历史
    taskProgress.currentProduct = productUploadResult
    taskProgress.processHistory.push({ ...productUploadResult })

    // 即使发生异常，也要检查是否继续下一个任务（关键修改：不再停止流程）
    if (taskResponse) {
      const isLastBatch = checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1)
      taskProgress.currentProgress = taskResponse.task_num + 1

      // 重置处理状态，准备继续下一个任务
      taskProgress.isProcessing = false

      if (isLastBatch) {
        // 当前批次已完成，但需要再次请求确认是否真的完成
        taskProgress.processingMessage = '当前批次已完成，正在检查是否还有更多任务...'
        setTimeout(() => {
          handleContinueNextBatch()
        }, 1000)
      } else {
        // 继续下一批任务，即使当前任务异常也要继续
        taskProgress.processingMessage = '当前商品处理异常，正在继续下一个...'
        setTimeout(() => {
          handleContinueNextBatch()
        }, 2000)
      }
    } else {
      // 如果没有taskResponse，说明是严重错误，但仍然尝试继续（除非是致命错误）
      console.warn('taskResponse为空，尝试继续下一批任务')
      taskProgress.isProcessing = false
      taskProgress.processingMessage = '遇到异常，正在尝试继续下一个...'

      // 即使没有taskResponse，也尝试继续下一批
      setTimeout(() => {
        handleContinueNextBatch()
      }, 3000)
    }
  }
}

// 继续下一批任务
const handleContinueNextBatch = async () => {
  if (!currentTask.value) return

  // 检查是否已取消任务
  if (aiRewriteCancelled.value || taskProgress.hasError) {
    return
  }

  console.log("739行 handleContinueNextBatch 中的 currentTask.value===", currentTask.value);
  try {
    taskProgress.isProcessing = true
    taskProgress.processingMessage = '正在获取下一个任务数据...'

    // 重新调用开始任务API获取下一批数据
    const response: TaskStartResponse = await startTask(currentTask.value.id)

    if (response.task_over === 1) {
      // 所有任务已完成，调用任务完成更新
      try {
        await updateTaskComplete(currentTask.value.id)
        console.log('任务完成状态更新成功')
      } catch (error: any) {
        console.error('更新任务完成状态失败:', error)
      }

      taskProgress.isProcessing = false
      taskProgress.isCompleted = true
      taskProgress.processingMessage = '所有任务已完成！'
      setTimeout(() => {
        loadTaskList()
      }, 3000)
    } else if (response.task_exist === 1) {
      //这种情况实际操作中已经不存在了 但是保留该判断
      // 又遇到已存在的商品，立即显示并跳过
      const productResult: ProductUploadResult = {
        taskDetailId: response.id || 0,
        success: false,
        message: '商品已存在，跳过处理',
        productName: response.goods_name || '未知商品',
        productImage: response.thumb_url || '',
        price: response.price && response.price_third ?
          `原价: ${response.price} ${response.currentcy_goods || ''} | 售价: ${response.price_third} ${response.currentcy || ''}` :
          (response.price_third ? `${response.price_third} ${response.currentcy || ''}` :
            response.price ? `${response.price} ${response.currentcy_goods || ''}` : '')
      }

      // 立即显示当前处理的商品
      taskProgress.currentProduct = productResult
      taskProgress.processHistory.push({ ...productResult })

      // 更新总进度数（如果服务器返回了更准确的数据）
      if (response.task_count && response.task_count > taskProgress.totalProgress) {
        taskProgress.totalProgress = response.task_count
      }

      // 使用服务器返回的task_num + 1作为当前进度
      if (response.task_num !== undefined) {
        taskProgress.currentProgress = response.task_num + 1
      } else {
        taskProgress.currentProgress = Math.min(
          taskProgress.currentProgress + 1,
          taskProgress.totalProgress
        )
      }
      taskProgress.processingMessage = '商品已存在，正在继续处理下一个...'

      // 继续下一个
      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    } else if (response.goods_no_cat_relation === 1) {
      //这种情况实际操作中已经不存在了 但是保留该判断
      // 遇到分类未关联的商品，记录错误但继续下一个
      const errorProductResult: ProductUploadResult = {
        taskDetailId: response.id || 0,
        success: false,
        message: '商品分类未关联，跳过此商品',
        productName: response.goods_name || '未知商品',
        productImage: response.thumb_url || '',
        price: response.price_third ? `${response.price_third} ${response.currentcy}` : ''
      }

      taskProgress.currentProduct = errorProductResult
      taskProgress.processHistory.push({ ...errorProductResult })

      // 更新总进度数（如果服务器返回了更准确的数据）
      if (response.task_count && response.task_count > taskProgress.totalProgress) {
        taskProgress.totalProgress = response.task_count
      }

      // 使用服务器返回的task_num + 1作为当前进度
      if (response.task_num !== undefined) {
        taskProgress.currentProgress = response.task_num + 1
      } else {
        taskProgress.currentProgress = Math.min(
          taskProgress.currentProgress + 1,
          taskProgress.totalProgress
        )
      }
      taskProgress.processingMessage = '分类未关联，跳过此商品，继续下一个...'

      // 继续下一个任务
      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    } else if (response.task_over === 0 && response.task_exist === 0 && response.goods_no_cat_relation === 0) {
      // 继续处理下一批正常任务
      // 确保response包含必要的字段
      if (response.id && response.task_id) {
        await handleNormalTask(response as TaskResponse, currentTask.value.store_name)
      } else {
        // 数据不完整，记录错误但继续下一个
        const errorProductResult: ProductUploadResult = {
          taskDetailId: 0,
          success: false,
          message: '任务响应数据不完整，跳过此商品',
          productName: '数据不完整',
          productImage: '',
          price: ''
        }

        taskProgress.currentProduct = errorProductResult
        //taskProgress.processHistory.push({ ...errorProductResult })
        taskProgress.processingMessage = '数据不完整，跳过此商品，继续下一个...'

        // 继续下一个任务
        setTimeout(() => {
          handleContinueNextBatch()
        }, 2000)
      }
    }
  } catch (error: any) {
    console.error('继续下一批任务失败:', error)
    taskProgress.isProcessing = false

    // 检查是否已取消任务或currentTask为空，如果是则直接返回，不显示错误
    if (aiRewriteCancelled.value || !currentTask.value || taskProgress.hasError) {
      return
    }

    // 判断错误类型，隐藏技术性错误信息
    let userFriendlyMessage = '网络连接异常，请稍后重试'
    let shouldShowError = true

    if (error.message) {
      // 隐藏技术性错误信息
      if (error.message.includes('Cannot read properties') ||
          error.message.includes('TypeError') ||
          error.message.includes('null')) {
        shouldShowError = false // 不显示这类技术错误
      } else if (error.message.includes('网络') || error.message.includes('timeout')) {
        userFriendlyMessage = '网络连接超时，正在重试...'
      }
    }

    // 只有在需要显示错误时才创建错误记录
    if (shouldShowError) {
      const errorProductResult: ProductUploadResult = {
        taskDetailId: 0,
        success: false,
        message: userFriendlyMessage,
        productName: '连接异常',
        productImage: '',
        price: ''
      }

      // 添加到处理历史
      taskProgress.currentProduct = errorProductResult
      taskProgress.processingMessage = userFriendlyMessage
      // 短暂延迟后继续请求下一批任务
      setTimeout(() => {
        handleContinueNextBatch()
      }, 3000) // 3秒后继续
    }
  }
}

// 处理进度对话框关闭
const handleProgressDialogClose = () => {
  progressDialogVisible.value = false
  // 重置取消标志
  aiRewriteCancelled.value = false
  isAiRewriteTask.value = false // 重置AI改写任务标志
  // 重置进度数据
  Object.assign(taskProgress, {
    currentProgress: 0,
    totalProgress: 0,
    isProcessing: false,
    isCompleted: false,
    hasError: false,
    errorMessage: '',
    processingMessage: '正在处理商品...',
    currentProduct: null,
    processHistory: []
  })
  currentTask.value = null
}

// 处理进度对话框取消
const handleProgressDialogCancel = () => {
  // 设置取消标志，停止AI改写循环
  aiRewriteCancelled.value = true

  // 停止当前任务处理
  taskProgress.isProcessing = false
  taskProgress.hasError = true
  taskProgress.errorMessage = '用户取消了任务'

  // 延迟清空currentTask，给正在执行的异步操作一些时间来检查取消状态
  setTimeout(() => {
    currentTask.value = null
  }, 100)
}

// 当前视图状态
const currentView = ref<'list' | 'detail'>('list')
const currentTaskId = ref<number | null>(null)

// 处理AI改写标题按钮点击
const handleAiRewriteTitle = async (row: Task) => {
  try {
    // 显示确认对话框
    const confirmResult = await ElMessageBox.confirm(
      `您确定要执行AI改写商品标题和描述操作吗？`,
      '确认AI改写',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (confirmResult === 'confirm') {
      // 设置当前任务
      currentTask.value = row
      isAiRewriteTask.value = true // 标记为AI改写任务
      aiRewritingTaskId.value = row.id

      // 显示加载对话框
      loadingTitle.value = '任务详情生成中'
      loadingMessage.value = `正在生成任务"${row.store_name}"的详情记录，请稍候...`
      loadingDialogVisible.value = true

      // 开始生成任务详情
      await handleGenerateTaskDetails(row)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('AI改写标题失败:', error)
      ElMessage.error('AI改写标题失败，请重试')
    }
  } finally {
    aiRewritingTaskId.value = null
  }
}

// 处理生成任务详情
const handleGenerateTaskDetails = async (task: Task) => {
  try {
    let offset = 0
    const limit = 100
    let hasMore = true

    while (hasMore) {
      const params: TaskNewGenerateDetailsParams = {
        task_id: task.id,
        offset: offset,
        limit: limit
      }

      const response: TaskNewGenerateDetailsResponse = await generateTaskDetails(params)

      hasMore = response.has_more
      offset = response.current_offset

      // 存储店铺品牌信息
      if (response.store_brand) {
        currentStoreBrand.value = response.store_brand
      }

      // 更新加载信息
      loadingMessage.value = `已生成 ${response.processed_count} 个商品详情记录...`

      // 如果还有更多数据，继续生成
      if (hasMore) {
        // 稍作延迟，避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
    // 强制刷新用户信息（积分已扣除）
    console.log('任务详情生成完成，正在刷新用户信息...')
    await forceRefreshUserInfo()
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 隐藏加载对话框
    loadingDialogVisible.value = false

    // 开始AI改写过程 先注释 调试用
    await handleAiRewriteProcess(task)

  } catch (error: any) {
    console.error('生成任务详情失败:', error)
    loadingDialogVisible.value = false
  }
}

// 处理AI改写过程
const handleAiRewriteProcess = async (task: Task) => {
  try {
    // 重置取消标志
    aiRewriteCancelled.value = false

    // 初始化进度数据
    Object.assign(taskProgress, {
      currentProgress: 0,
      totalProgress: 0,
      isProcessing: false,
      isCompleted: false,
      hasError: false,
      errorMessage: '',
      processingMessage: '正在初始化AI改写...',
      currentProduct: null,
      processHistory: []
    })

    // 获取初始进度
    const progressResponse: TaskNewAiProgressResponse = await getAiProgress(task.id)
    taskProgress.totalProgress = progressResponse.total_count
    taskProgress.currentProgress = progressResponse.completed_count

    console.log('progressResponse', progressResponse)

    // 显示进度对话框
    progressDialogVisible.value = true

    // 开始AI改写循环
    await handleAiRewriteLoop(task)

  } catch (error: any) {
    console.error('AI改写过程失败:', error)
  }
}

// AI改写循环处理
const handleAiRewriteLoop = async (task: Task) => {
  try {
    // 检查是否已取消
    if (aiRewriteCancelled.value) {
      taskProgress.isProcessing = false
      taskProgress.hasError = true
      taskProgress.errorMessage = '用户取消了AI改写任务'
      return
    }

    taskProgress.isProcessing = true
    taskProgress.processingMessage = '正在获取下一个待处理的商品...'

    // 获取下一个待AI改写的任务
    const nextTaskResponse: TaskNewNextAiTaskResponse = await getNextAiTask({ task_id: task.id })
    console.log('nextTaskResponse', nextTaskResponse)

    if (!nextTaskResponse.has_task) {
      // 所有任务都已完成
      await completeAiTask(task.id)
      taskProgress.isProcessing = false
      taskProgress.isCompleted = true
      taskProgress.processingMessage = '所有商品的AI改写已完成！'
      // 更新任务列表
      setTimeout(() => {
        loadTaskList()
      }, 3000)
      return
    }

    const taskDetail = nextTaskResponse.task_detail!

    // 显示当前处理的商品，使用新的接口字段
    taskProgress.currentProduct = {
      taskDetailId: taskDetail.id,
      success: false,
      message: '正在AI改写中...',
      productName: taskDetail.goods_name,
      productImage: taskDetail.thumb_url || '',
      price: taskDetail.price && taskDetail.price_third ?
        `原价: ${taskDetail.price} ${taskDetail.currentcy_goods || ''} | 售价: ${taskDetail.price_third} ${taskDetail.currentcy || ''}` :
        (taskDetail.price_third ? `${taskDetail.price_third} ${taskDetail.currentcy || ''}` :
          taskDetail.price ? `${taskDetail.price} ${taskDetail.currentcy_goods || ''}` : '')
    }

    // 执行AI改写
    await handleSingleAiRewrite(taskDetail, task)

  } catch (error: any) {
    console.error('AI改写循环失败:', error)
    taskProgress.isProcessing = false
    taskProgress.hasError = true
    taskProgress.errorMessage = 'AI改写失败：' + (error.message || '未知错误')
  }
}

// 处理单个商品的AI改写
const handleSingleAiRewrite = async (taskDetail: TaskDetailForAi, task: Task) => {
  try {
    // 检查是否已取消
    if (aiRewriteCancelled.value) {
      taskProgress.isProcessing = false
      taskProgress.hasError = true
      taskProgress.errorMessage = '用户取消了AI改写任务'
      return
    }

    // 更新处理状态为AI改写中
    taskProgress.processingMessage = '正在AI改写商品标题和描述...'

    // 获取AI Key
    taskProgress.processingMessage = '正在获取AI密钥...'
    const aiKeyResponse = await getRandomAiKey()
    const aiKey = aiKeyResponse.ai_key
    console.log('aiKey', aiKey)

    // 提取SKU值
    taskProgress.processingMessage = '正在分析商品信息...'
    const skuValues = extractSkuValues(taskDetail.spec_values)
    console.log('currentStoreBrand', currentStoreBrand.value)

    // 构建标题提示词
    const titlePrompt = constructTitlePrompt(skuValues, taskDetail.goods_name)
    console.log('titlePrompt', titlePrompt)

    // 调用AI改写标题
    taskProgress.processingMessage = '正在AI改写商品标题...'
    const aiTitle = await callDeepSeekApi(titlePrompt, aiKey)
    console.log('aiTitle', aiTitle)

    // 获取店铺信息用于品牌前缀
    const brandPrefix = currentStoreBrand.value || ''
    console.log('brandPrefix', brandPrefix)

    // 添加品牌前缀
    const finalTitle = prependBrand(aiTitle, brandPrefix)
    console.log('finalTitle', finalTitle)

    // 改写描述（如果需要）
    let aiProperty = ''
    if (taskDetail.goods_property && taskDetail.is_property_ai == 0) {
      taskProgress.processingMessage = '正在AI改写商品描述...'
      const propertyPrompt = constructPropertyPrompt(taskDetail.goods_property)
      console.log('propertyPrompt', propertyPrompt)
      aiProperty = await callDeepSeekApi(propertyPrompt, aiKey)
      console.log('aiProperty', aiProperty)
    }

    // 更新AI改写结果
    taskProgress.processingMessage = '正在保存AI改写结果...'
    await updateAiResult({
      task_detail_id: taskDetail.id,
      goods_name_ai: finalTitle,
      goods_property_ai: aiProperty
    })

    // 更新当前商品状态
    if (taskProgress.currentProduct) {
      taskProgress.currentProduct.success = true
      taskProgress.currentProduct.message = 'AI改写成功'
      taskProgress.currentProduct.productName = finalTitle // 使用AI改写后的标题
      taskProgress.processHistory.push({ ...taskProgress.currentProduct })
    }

    // 更新进度
    taskProgress.currentProgress += 1

    // 继续下一个商品
    setTimeout(() => {
      handleAiRewriteLoop(task)
    }, 1000)

  } catch (error: any) {
    console.error('单个商品AI改写失败:', error)

    // 检查是否已取消
    if (aiRewriteCancelled.value) {
      taskProgress.isProcessing = false
      taskProgress.hasError = true
      taskProgress.errorMessage = '用户取消了AI改写任务'
      return
    }

    // 更新当前商品状态，显示详细的错误信息
    if (taskProgress.currentProduct) {
      taskProgress.currentProduct.success = false

      // 根据错误类型显示不同的错误信息
      if (error.message && error.message.includes('DeepSeek API')) {
        taskProgress.currentProduct.message = `AI改写失败: ${error.message}`
      } else {
        taskProgress.currentProduct.message = `AI改写失败: ${error.message || '未知错误'}`
      }

      // 不添加到处理历史：失败的会在下次继续处理
      // taskProgress.processHistory.push({ ...taskProgress.currentProduct })
    }

    // 即使失败也继续下一个
    setTimeout(() => {
      handleAiRewriteLoop(task)
    }, 1000)
  }
}

// 获取店铺信息（模拟函数，需要根据实际API调整）
const getStoreInfo = async (userAccountId: number) => {
  // 这里需要调用实际的店铺信息API
  // 暂时返回空对象
  return { brand: '' }
}

// 处理查看详情按钮点击
const handleViewDetails = (row: Task) => {
  console.log('查看详情:', row)
  currentTaskId.value = row.id
  currentView.value = 'detail'
}

// 返回任务列表
const handleBackToList = () => {
  currentView.value = 'list'
  currentTaskId.value = null
}

// 格式化时间范围 (原有的，可能需要调整或移除)
const formatTimeRange = (row: Task) => {
  if (row.time_range === 'today') {
    return '今天'
  } else if (row.time_range === 'yesterday') {
    return '昨天'
  } else if (row.time_range === 'custom' && row.day_start && row.day_end) {
    return `${row.day_start} 至 ${row.day_end}`
  }
  return '未知'
}

// 处理日期范围变化
const handleDateRangeChange = (val: string[]) => {
  if (val && val.length === 2) {
    searchForm.day_start = val[0]
    searchForm.day_end = val[1]
  } else {
    searchForm.day_start = undefined
    searchForm.day_end = undefined
  }
}

// 加载任务列表
const loadTaskList = async () => {
  loading.value = true
  try {
    const params: TaskListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      task_over: searchForm.task_over,
      day_start: searchForm.day_start,
      day_end: searchForm.day_end,
      has_rejected: searchForm.has_rejected
    }

    const response = await getTaskList(params)
    console.log('获取任务列表成功:', response)
    taskList.value = response.list as Task[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadTaskList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    task_over: undefined,
    day_start: undefined,
    day_end: undefined,
    has_rejected: false
  })
  dateRange.value = []
  pagination.currentPage = 1
  loadTaskList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTaskList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadTaskList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadTaskList()
})

// 获取进度对话框标题
const getProgressDialogTitle = () => {
  const storeName = currentTask.value?.store_name || ''
  if (isAiRewriteTask.value) {
    return storeName ? `${storeName} - AI改写进度` : 'AI改写进度'
  } else {
    return storeName ? `${storeName} - 任务执行进度` : '任务执行进度'
  }
}
</script>

<style scoped>
.task-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.status-item {
  width: 260px;
  /* 根据需要调整宽度 */
}

.rejected-count {
  color: #f56c6c;
  font-weight: bold;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

/* 任务ID信息样式 */
.task-id-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 货币信息样式 */
.currency-info {
  font-size: 12px;
  color: #409eff;
  margin-top: 4px;
  font-weight: 500;
}

/* 汇率货币信息样式 */
.exchange-currency-info {
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
  font-weight: 500;
}

/* 状态容器样式 */
.status-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.ai-status-row,
.task-status-row {
  width: 100%;
  text-align: center;
}

.ai-status-row .el-tag,
.task-status-row .el-tag {
  width: 100%;
  font-size: 12px;
  padding: 2px 4px;
}

/* 当两个状态都未完成时的特殊样式 */
.status-container:has(.ai-status-row .el-tag--danger):has(.task-status-row .el-tag--warning) .el-tag {
  border: 1px solid #f56c6c;
  background-color: #fef0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>
