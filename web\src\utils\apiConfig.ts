// API配置文件
export const API_URLS = {
  // 用户相关API
  USER_INFO: 'apiUserInfoUrl',
  TASK_ADD: 'apiTaskAddUrl',
  TASK_LIST: 'apiTaskListUrl',
  TASK_START: 'apiTaskStartUrl',
  TASK_UPDATE: 'apiTaskUpdateUrl',
  TASK_DETAIL: 'apiTaskDetailUrl',
  TASK_DETAIL_LIST: 'apiTaskDetailListUrl',
  TASK_QUERY_RESULTS: 'apiTaskQueryResultsUrl',
  TASK_PENDING_QUERY: 'apiTaskPendingQueryUrl',
  TASK_BATCH_UPDATE: 'apiTaskBatchUpdateUrl',
  TASK_SAVE_UPLOAD_PARAMS: 'apiTaskSaveUploadParamsUrl',
  TASK_RETRY_UPLOAD_PARAMS: 'apiTaskRetryUploadParamsUrl',
  TASK_BATCH_RETRY_UPLOAD: 'apiTaskBatchRetryUploadUrl',
  TASK_BATCH_RETRY_UPLOAD_BY_STATUS: 'apiTaskBatchRetryUploadByStatusUrl',
  TASK_UPDATE_CATEGORY: 'apiTaskUpdateCategoryUrl',

  // 分类相关API
  CATEGORY_N11_LIST: 'apiCatN11ListUrl',
  CATEGORY_LINK: 'apiCategoryLinkUrl',
  CATEGORY_RELATION_LIST: 'apiCatRelationListUrl',

  // 商品相关API
  GOODS_STATISTICS: 'apiGoodsStatisticsUrl',
  GOODS_CAT_RELATION_GET: 'apiGoodsCatRelationGetUrl',
  GOODS_CAT_RELATION_SAVE: 'apiGoodsCatRelationSaveUrl',
  GOODS_CAT_RELATION_DELETE: 'apiGoodsCatRelationDeleteUrl',
  GOODS_CAT_RELATION_BATCH_GET: 'apiGoodsCatRelationBatchGetUrl',

  // 卡密管理相关API
  CARD_CODE_LIST: 'apiCardCodeListUrl',
  CARD_CODE_CREATE: 'apiCardCodeCreateUrl',
  CARD_CODE_BATCH_CREATE: 'apiCardCodeBatchCreateUrl',
  CARD_CODE_UPDATE: 'apiCardCodeUpdateUrl',
  CARD_CODE_DELETE: 'apiCardCodeDeleteUrl',
  CARD_CODE_BATCH_DELETE: 'apiCardCodeBatchDeleteUrl',
  CARD_CODE_DETAIL: 'apiCardCodeDetailUrl',
  CARD_CODE_STATISTICS: 'apiCardCodeStatisticsUrl',
  CARD_CODE_USE_CARD: 'apiCardCodeUseCardUrl',
  CARD_CODE_USER_USAGE_RECORDS: 'apiCardCodeUserUsageRecordsUrl',
  CARD_CODE_USAGE_RECORDS: 'apiCardCodeUsageRecordsUrl',
  CARD_CODE_CARD_TYPE_OPTIONS: 'apiCardCodeCardTypeOptionsUrl',
  CARD_CODE_STATUS_OPTIONS: 'apiCardCodeStatusOptionsUrl',
  CARD_CODE_COPY: 'apiCardCodeCopyUrl',
  CARD_CODE_VIP_DAYS_UNIT_OPTIONS: 'apiCardCodeVipDaysUnitOptionsUrl',
  CARD_CODE_COPY_STATUS_OPTIONS: 'apiCardCodeCopyStatusOptionsUrl',
  CARD_CODE_MEMBER_OPTIONS: 'apiCardCodeMemberOptionsUrl',

  // AI Key管理相关API
  AI_KEY_LIST: 'apiAiKeyListUrl',
  AI_KEY_CREATE: 'apiAiKeyCreateUrl',
  AI_KEY_UPDATE: 'apiAiKeyUpdateUrl',
  AI_KEY_DELETE: 'apiAiKeyDeleteUrl',
  AI_KEY_DETAIL: 'apiAiKeyDetailUrl',
  AI_KEY_BATCH_UPDATE: 'apiAiKeyBatchUpdateUrl',

  // 积分相关API
  POINTS_LOGS: 'apiPointsLogsUrl',
  POINTS_CHECK_TASK_DEDUCTED: 'apiPointsCheckTaskDeductedUrl',

  // API凭证管理相关API
  GENERATE_API_CREDENTIALS: 'apiGenerateCredentialsUrl',

  // 子账号管理相关API
  SUB_ACCOUNT_LIST: 'apiSubAccountListUrl',
  SUB_ACCOUNT_CREATE: 'apiSubAccountCreateUrl',
  SUB_ACCOUNT_UPDATE: 'apiSubAccountUpdateUrl',
  SUB_ACCOUNT_DELETE: 'apiSubAccountDeleteUrl',
  SUB_ACCOUNT_TOGGLE_STATUS: 'apiSubAccountToggleStatusUrl',
  SUB_ACCOUNT_DETAIL: 'apiSubAccountDetailUrl',

  // 子账号商品管理相关API
  SUB_ACCOUNT_GOODS_LIST: 'apiSubAccountGoodsListUrl',
  SUB_ACCOUNT_GOODS_DELETE: 'apiSubAccountGoodsDeleteUrl',

  // 其他平台API（预留）
  // CATEGORY_AMAZON_LIST: 'apiCatAmazonListUrl',
  // CATEGORY_EBAY_LIST: 'apiCatEbayListUrl',
}

// 平台配置
export const PLATFORMS = {
  N11: {
    id: 1,
    name: 'N11',
    code: 'n11',
    apiUrl: API_URLS.CATEGORY_N11_LIST
  }
  // 可以在这里添加更多平台
}

// 关联类型
export const LINK_TYPES = {
  SYSTEM: 1,
  USER: 2
} as const

export type LinkType = typeof LINK_TYPES[keyof typeof LINK_TYPES]

/**
 * API配置工具
 * 统一管理API地址获取逻辑
 */

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
export const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};
