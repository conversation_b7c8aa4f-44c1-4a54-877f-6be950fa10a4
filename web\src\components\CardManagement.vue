<template>
  <div class="card-management">
    <!-- 权限检查 -->
    <div v-if="!hasCardAdminPermission" class="no-permission">
      <el-result
        icon="warning"
        title="权限不足"
        sub-title="您没有卡密管理权限，请联系管理员获取权限"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/dashboard')">返回控制面板</el-button>
        </template>
      </el-result>
    </div>

    <!-- 卡密管理内容 -->
    <div v-else>
      <div class="page-header">
      <h2>卡密管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          生成卡密
        </el-button>
        <el-button type="success" @click="showBatchCreateDialog = true">
          <el-icon><DocumentAdd /></el-icon>
          批量生成
        </el-button>
        <el-button type="info" @click="refreshStatistics(true)">
          <el-icon><Refresh /></el-icon>
          更新统计
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="16">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_count }}</div>
              <div class="stat-amount">¥{{ statistics.total_amount || 0 }}</div>
              <div class="stat-label">总卡密数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.unused_count }}</div>
              <div class="stat-amount">¥{{ statistics.unused_amount || 0 }}</div>
              <div class="stat-label">未使用</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.used_count }}</div>
              <div class="stat-amount">¥{{ statistics.used_amount || 0 }}</div>
              <div class="stat-label">已使用</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.copied_count || 0 }}</div>
              <div class="stat-amount">¥{{ statistics.copied_amount || 0 }}</div>
              <div class="stat-label">已复制</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.not_copied_count || 0 }}</div>
              <div class="stat-amount">¥{{ statistics.not_copied_amount || 0 }}</div>
              <div class="stat-label">未复制</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 时间筛选 -->
    <el-card class="time-filter-card">
      <div class="time-filter-header">
        <div class="time-type-selector">
          <el-select v-model="timeFilter.timeType" placeholder="选择时间类型" style="width: 120px;" @change="handleTimeTypeChange">
            <el-option label="创建时间" value="created_at" />
            <el-option label="使用时间" value="used_at" />
            <el-option label="复制时间" value="copied_at" />
          </el-select>
        </div>
        <div class="time-range-tabs">
          <el-tabs v-model="timeFilter.activeTab" @tab-change="handleTimeTabChange">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="今天" name="today" />
            <el-tab-pane label="昨天" name="yesterday" />
            <el-tab-pane label="本周" name="thisWeek" />
            <el-tab-pane label="本月" name="thisMonth" />
            <el-tab-pane label="上月" name="lastMonth" />
            <el-tab-pane label="上半年" name="firstHalf" />
            <el-tab-pane label="下半年" name="secondHalf" />
            <el-tab-pane label="今年" name="thisYear" />
            <el-tab-pane label="去年" name="lastYear" />
          </el-tabs>
        </div>
        <!-- 自定义时间选择器 -->
        <div class="time-filter-actions">
          <el-form inline>
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="timeFilter.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                size="small"
                style="width: 140px;"
                @change="handleCustomDateChange"
              />
            </el-form-item>
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="timeFilter.endDate"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                size="small"
                style="width: 140px;"
                @change="handleCustomDateChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="applyCustomDateRange">确定</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 搜索筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="卡密">
          <el-input v-model="searchForm.card_code" placeholder="请输入卡密" clearable />
        </el-form-item>
        <el-form-item label="卡密名称">
          <el-input v-model="searchForm.card_name" placeholder="请输入卡密名称" clearable />
        </el-form-item>
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.card_type" style="min-width: 100px;" placeholder="请选择类型" clearable>
            <el-option label="有效期卡" :value="1" />
            <el-option label="积分卡" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" style="min-width: 100px;" placeholder="请选择状态" clearable>
            <el-option label="未使用" :value="1" />
            <el-option label="已使用" :value="0" />
            <!-- <el-option label="已禁用" :value="2" /> -->
          </el-select>
        </el-form-item>
        <el-form-item label="复制状态">
          <el-select v-model="searchForm.is_copied" style="min-width: 100px;" placeholder="请选择状态" clearable>
            <el-option label="未复制" :value="0" />
            <el-option label="已复制" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="卡密单位">
          <el-select v-model="searchForm.vip_days_unit" style="min-width: 100px;" placeholder="请选择单位" clearable>
            <el-option label="年" value="year" />
            <el-option label="月" value="month" />
            <el-option label="天" value="day" />
          </el-select>
        </el-form-item>
        <el-form-item label="复制人">
          <el-select v-model="searchForm.copied_by" style="min-width: 120px;" placeholder="请选择复制人" clearable>
            <el-option
              v-for="member in memberOptions"
              :key="member.value"
              :label="member.label"
              :value="member.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建人">
          <el-select v-model="searchForm.admin_id" style="min-width: 120px;" placeholder="请选择创建人" clearable>
            <el-option
              v-for="member in memberOptions"
              :key="member.value"
              :label="member.label"
              :value="member.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="批次号">
          <el-input v-model="searchForm.batch_no" placeholder="请输入批次号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 卡密列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">卡密列表</div>
        <div class="table-actions">
          <el-button
            type="danger"
            :disabled="selectedCards.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
      </div>

      <el-table
        :data="cardList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" :selectable="isRowSelectable" />
        <el-table-column prop="card_code" label="卡密" min-width="250">
          <template #default="scope">
            <div class="card-info-cell">
              <div class="card-code-line">{{ scope.row.card_code }}</div>
              <div class="card-type-line">{{ scope.row.card_type_text }}{{ scope.row.card_name ? '-' + scope.row.card_name : '' }}</div>
              <div v-if="scope.row.batch_no" class="batch-no-line">批次：{{ scope.row.batch_no }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信息" width="150">
          <template #default="scope">
            <div class="value-info-cell">
              <div class="price-line">¥{{ scope.row.price }}</div>
              <div class="points-line">{{ scope.row.points }}积分</div>
              <div v-if="scope.row.card_type === 1 && scope.row.vip_days > 0" class="vip-line">
                {{ scope.row.vip_days }}{{ getVipDaysUnitText(scope.row.vip_days_unit) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="复制卡密" width="180">
          <template #default="scope">
            <div class="copy-info-cell">
              <div class="copy-status-line">
                <el-tag :type="scope.row.is_copied ? 'success' : 'info'" size="small">
                  {{ scope.row.is_copied ? '已复制' : '未复制' }}
                </el-tag>
              </div>
              <div v-if="scope.row.is_copied && scope.row.copied_by_phone" class="copied-info-line">
                复制人：{{ scope.row.copied_by_phone }}
              </div>
              <div v-if="scope.row.is_copied && scope.row.copied_at" class="copied-time-line">
                {{ scope.row.copied_at }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="卡密状态" width="180">
          <template #default="scope">
            <div class="status-info-cell">
              <div class="status-line">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ scope.row.status_text }}
                </el-tag>
              </div>
              <div v-if="scope.row.status === 0 && scope.row.used_by_phone" class="used-info-line">
                使用人：{{ scope.row.used_by_phone }}
              </div>
              <div v-if="scope.row.status === 0 && scope.row.used_at" class="used-time-line">
                {{ scope.row.used_at }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="admin_phone" label="创建人" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button
              v-if="shouldShowCopyButton(scope.row)"
              type="success"
              size="small"
              @click="handleCopy(scope.row)"
            >
              复制
            </el-button>
            <el-button
              v-if="(scope.row.status === 1 && !scope.row.is_copied) || (scope.row.status === 2 && !scope.row.is_copied)"
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 卡密对话框组件 -->
    <CardDialog
      v-model="showCreateDialog"
      :is-batch-mode="false"
      :loading="createLoading"
      @confirm="handleCreate"
      @cancel="handleCreateCancel"
    />

    <!-- 批量生成对话框组件 -->
    <CardDialog
      v-model="showBatchCreateDialog"
      :is-batch-mode="true"
      :loading="batchCreateLoading"
      @confirm="handleBatchCreate"
      @cancel="handleBatchCreateCancel"
    />

    <!-- 卡密详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      title="卡密详情"
      width="600px"
      :before-close="handleCloseDetail"
      class="card-detail-dialog"
    >
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-item">
          <label class="detail-label">卡密：</label>
          <div class="detail-value card-code-detail">
            <span class="card-code-full">{{ currentDetail.card_code }}</span>
            <el-button
              v-if="currentDetail.is_copied && isCardCopier(currentDetail)"
              type="primary"
              size="small"
              class="copy-btn-detail"
              @click="copyToClipboard(currentDetail.card_code, '卡密')"
            >
              复制
            </el-button>
          </div>
        </div>

        <div class="detail-item">
          <label class="detail-label">卡密名称：</label>
          <span class="detail-value">{{ currentDetail.card_name }}</span>
        </div>

        <div class="detail-item">
          <label class="detail-label">类型：</label>
          <el-tag :type="currentDetail.card_type === 1 ? 'success' : 'warning'">
            {{ currentDetail.card_type_text }}
          </el-tag>
        </div>

        <div class="detail-item">
          <label class="detail-label">价格：</label>
          <span class="detail-value price-value">¥{{ currentDetail.price }}</span>
        </div>

        <div class="detail-item">
          <label class="detail-label">积分：</label>
          <span class="detail-value points-value">{{ currentDetail.points }}</span>
        </div>

        <div class="detail-item" v-if="currentDetail.vip_days && currentDetail.vip_days_unit">
          <label class="detail-label">VIP时长：</label>
          <span class="detail-value vip-value">
            {{ currentDetail.vip_days }}{{ getVipDaysUnitText(currentDetail.vip_days_unit) }}
          </span>
        </div>

        <div class="detail-item">
          <label class="detail-label">状态：</label>
          <el-tag :type="getStatusTagType(currentDetail.status)">
            {{ currentDetail.status_text }}
          </el-tag>
        </div>

        <!-- 使用状态显示 -->
        <div class="detail-item" v-if="currentDetail.status === 0 && currentDetail.used_by_phone">
          <label class="detail-label">使用信息：</label>
          <div class="detail-value">
            <div class="usage-info">
              <span class="usage-user">{{ currentDetail.used_by_phone }}</span>
              <span class="usage-time">{{ currentDetail.used_at }}</span>
            </div>
          </div>
        </div>

        <div class="detail-item">
          <label class="detail-label">复制状态：</label>
          <el-tag :type="currentDetail.is_copied ? 'success' : 'info'">
            {{ currentDetail.is_copied ? '已复制' : '未复制' }}
          </el-tag>
        </div>

        <!-- 复制状态显示 -->
        <div class="detail-item" v-if="currentDetail.is_copied === 1 && currentDetail.copied_by_phone">
          <label class="detail-label">复制信息：</label>
          <div class="detail-value">
            <div class="copy-info">
              <span class="copy-user">{{ currentDetail.copied_by_phone }}</span>
              <span class="copy-time">{{ currentDetail.copied_at }}</span>
            </div>
          </div>
        </div>

        <div class="detail-item" v-if="currentDetail.batch_no">
          <label class="detail-label">批次号：</label>
          <span class="detail-value">{{ currentDetail.batch_no }}</span>
        </div>

        <div class="detail-item">
          <label class="detail-label">创建人：</label>
          <span class="detail-value">{{ currentDetail.admin_phone }}</span>
        </div>

        <div class="detail-item">
          <label class="detail-label">创建时间：</label>
          <span class="detail-value">{{ currentDetail.created_at }}</span>
        </div>

        <div class="detail-item">
          <label class="detail-label">描述：</label>
          <span class="detail-value">{{ currentDetail.description || '无' }}</span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDetail">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, DocumentAdd, Refresh } from '@element-plus/icons-vue'
import { userInfo } from '../utils/userStore'
import CardDialog from './cardManagement/CardDialog.vue'
import {
  getCardCodeList,
  createCardCode,
  batchCreateCardCodes,
  deleteCardCode,
  batchDeleteCardCodes,
  getCardCodeDetail,
  getCardCodeStatistics,
  copyCardCode,
  getMemberOptions,
  type CardCode,
  type CardCodeListParams,
  type CardCodeFormData,
  type CardCodeBatchCreateData,
  type CardCodeStatisticsParams,
  type CardCodeStatistics
} from '../utils/cardCodeApi'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const batchCreateLoading = ref(false)
const showCreateDialog = ref(false)
const showBatchCreateDialog = ref(false)
const selectedCards = ref([])
const memberOptions = ref([])

// 权限检查
const hasCardAdminPermission = computed(() => {
  return userInfo.isCardAdmin === true
})

// 判断当前用户是否为卡密复制人
const isCardCopier = (row: CardCode) => {
  return row.copied_by === userInfo.userId
}

// 判断是否显示复制按钮
const shouldShowCopyButton = (row: CardCode) => {
  // 如果卡密未被复制或使用，且当前用户是卡密管理员，则显示
  if (!row.is_copied && row.status === 1 && hasCardAdminPermission.value) {
    return true
  }
  // 如果卡密已被复制，且当前用户是复制人，则显示
  if (row.is_copied && isCardCopier(row)) {
    return true
  }
  return false
}

// 判断复制按钮是否需要确认
const needCopyConfirmation = (row: CardCode) => {
  // 如果卡密已被复制且当前用户是复制人，则不需要确认
  if (row.is_copied && isCardCopier(row)) {
    return false
  }
  // 其他情况需要确认
  return true
}



// 统计数据
const statistics = reactive({
  total_count: 0,
  unused_count: 0,
  used_count: 0,
  disabled_count: 0,
  vip_card_count: 0,
  points_card_count: 0,
  today_created: 0,
  today_used: 0,
  month_created: 0,
  month_used: 0,
  copied_count: 0,
  not_copied_count: 0,
  total_amount: 0,
  unused_amount: 0,
  used_amount: 0,
  disabled_amount: 0,
  copied_amount: 0,
  not_copied_amount: 0
})

// 时间筛选
const timeFilter = reactive({
  timeType: 'created_at', // 默认按创建时间筛选
  activeTab: 'thisMonth', // 默认选中本月
  startDate: '',
  endDate: ''
})

// 搜索表单
const searchForm = reactive({
  card_code: '',
  card_name: '',
  card_type: null,
  status: null,
  is_copied: null,
  copied_by: null,
  admin_id: null,
  batch_no: '',
  start_date: '',
  end_date: '',
  min_price: null,
  max_price: null,
  min_points: null,
  max_points: null,
  vip_days_unit: null, // 卡密单位
  time_type: 'created_at', // 时间类型
  date_range: 'thisMonth' // 时间范围
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 卡密列表
const cardList = ref([])

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'info'    // 已使用
    case 1: return 'success' // 未使用
    case 2: return 'danger'  // 已禁用
    default: return 'info'
  }
}

// 获取时长单位文本
const getVipDaysUnitText = (unit: string) => {
  switch (unit) {
    case 'year': return '年'
    case 'month': return '月'
    case 'day': return '天'
    default: return '天'
  }
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedCards.value = selection
}

// 时间筛选处理
const getDateRange = (range: string) => {
  // 直接使用本地时间，浏览器会自动处理时区
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()
  const date = now.getDate()

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (d: Date) => {
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  switch (range) {
    case 'all':
      return { start: '', end: '' }
    case 'today':
      return {
        start: formatDate(now),
        end: formatDate(now)
      }
    case 'yesterday':
      const yesterday = new Date(year, month, date - 1)
      return {
        start: formatDate(yesterday),
        end: formatDate(yesterday)
      }
    case 'thisWeek':
      const dayOfWeek = now.getDay()
      const startOfWeek = new Date(year, month, date - dayOfWeek + 1) // 周一
      return {
        start: formatDate(startOfWeek),
        end: formatDate(now)
      }
    case 'thisMonth':
      const startOfMonth = new Date(year, month, 1)
      return {
        start: formatDate(startOfMonth),
        end: formatDate(now)
      }
    case 'lastMonth':
      const lastMonth = new Date(year, month - 1, 1)
      const endOfLastMonth = new Date(year, month, 0)
      return {
        start: formatDate(lastMonth),
        end: formatDate(endOfLastMonth)
      }
    case 'firstHalf':
      const startOfYear = new Date(year, 0, 1)
      const endOfFirstHalf = new Date(year, 5, 30) // 6月30日
      return {
        start: formatDate(startOfYear),
        end: formatDate(endOfFirstHalf)
      }
    case 'secondHalf':
      const startOfSecondHalf = new Date(year, 6, 1) // 7月1日
      const endOfYear = new Date(year, 11, 31) // 12月31日
      return {
        start: formatDate(startOfSecondHalf),
        end: formatDate(endOfYear)
      }
    case 'thisYear':
      const thisYearStart = new Date(year, 0, 1)
      return {
        start: formatDate(thisYearStart),
        end: formatDate(now)
      }
    case 'lastYear':
      const lastYearStart = new Date(year - 1, 0, 1)
      const lastYearEnd = new Date(year - 1, 11, 31)
      return {
        start: formatDate(lastYearStart),
        end: formatDate(lastYearEnd)
      }
    default:
      return { start: '', end: '' }
  }
}

const handleTimeTabChange = (tabName: string) => {
  timeFilter.activeTab = tabName
  const dateRange = getDateRange(tabName)
  searchForm.start_date = dateRange.start
  searchForm.end_date = dateRange.end
  searchForm.time_type = timeFilter.timeType
  searchForm.date_range = tabName

  // 自动搜索
  pagination.page = 1
  loadCardList()
  refreshStatistics()
}

const handleTimeTypeChange = () => {
  // 时间类型变化时，重新应用当前的时间范围
  if (timeFilter.activeTab !== 'all') {
    const dateRange = getDateRange(timeFilter.activeTab)
    searchForm.start_date = dateRange.start
    searchForm.end_date = dateRange.end
  }
  searchForm.time_type = timeFilter.timeType

  // 自动搜索
  pagination.page = 1
  loadCardList()
  refreshStatistics()
}

const handleCustomDateChange = () => {
  // 当自定义日期改变时的处理
}

const applyCustomDateRange = () => {
  if (!timeFilter.startDate || !timeFilter.endDate) {
    ElMessage.warning('请选择开始和结束日期')
    return
  }

  if (timeFilter.startDate > timeFilter.endDate) {
    ElMessage.warning('开始日期不能大于结束日期')
    return
  }

  searchForm.start_date = timeFilter.startDate
  searchForm.end_date = timeFilter.endDate
  searchForm.time_type = timeFilter.timeType
  searchForm.date_range = 'custom'

  // 自动切换到"全部"Tab
  timeFilter.activeTab = 'all'

  // 自动搜索
  pagination.page = 1
  loadCardList()
  refreshStatistics()
}



// 搜索
const handleSearch = () => {
  // 同步时间筛选参数
  searchForm.time_type = timeFilter.timeType
  searchForm.date_range = timeFilter.activeTab

  pagination.page = 1
  loadCardList()
  refreshStatistics()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    card_code: '',
    card_name: '',
    card_type: null,
    status: null,
    is_copied: null,
    copied_by: null,
    admin_id: null,
    batch_no: '',
    start_date: '',
    end_date: '',
    min_price: null,
    max_price: null,
    min_points: null,
    max_points: null,
    vip_days_unit: null,
    time_type: 'created_at',
    date_range: 'thisMonth'
  })

  // 重置时间筛选
  timeFilter.timeType = 'created_at'
  timeFilter.activeTab = 'thisMonth'
  timeFilter.startDate = ''
  timeFilter.endDate = ''

  // 重新设置默认时间范围
  const dateRange = getDateRange('thisMonth')
  searchForm.start_date = dateRange.start
  searchForm.end_date = dateRange.end

  pagination.page = 1
  loadCardList()
  refreshStatistics()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadCardList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadCardList()
}

// 查看详情
const showDetailDialog = ref(false)
const currentDetail = ref(null)

const handleView = async (row: CardCode) => {
  try {
    const detail = await getCardCodeDetail(row.id)
    console.log('卡密详情:', detail)
    currentDetail.value = detail
    showDetailDialog.value = true
  } catch (error) {
    console.error('获取卡密详情失败:', error)
    ElMessage.error('获取卡密详情失败')
  }
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentDetail.value = null
}



// 复制卡密
const handleCopy = async (row: CardCode) => {
  try {
    // 如果是复制人且卡密已被复制，直接复制到剪贴板
    if (row.is_copied && isCardCopier(row)) {
      copyToClipboard(row.card_code, '卡密')
      return
    }

    // 如果需要确认，先显示确认对话框
    if (needCopyConfirmation(row)) {
      await ElMessageBox.confirm(
        '您确定要复制该卡密吗？',
        '确认复制',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    }

    // 弹出备注编辑框
    ElMessageBox.prompt('请输入备注信息（可选）', '复制卡密', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入备注信息...'
    }).then(async ({ value }) => {
      try {
        // 调用复制接口获取完整卡密
        const response = await copyCardCode(row.id, value || '')

        // 复制完整卡密到剪贴板
        copyToClipboard(response.card_code, '卡密')

        // 刷新列表和统计信息
        await loadCardList()
        await refreshStatistics()
      } catch (error) {
        console.error('复制卡密失败:', error)
        ElMessage.error('复制卡密失败')
      }
    }).catch(() => {

    })
  } catch (error) {
    if (error === 'cancel') {

    } else {
      console.error('复制卡密操作失败:', error)
      ElMessage.error('复制卡密操作失败')
    }
  }
}

// 删除
const handleDelete = (row: CardCode) => {
  ElMessageBox.confirm(
    `确定要删除卡密 "${row.card_code}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteCardCode(row.id)
      ElMessage.success('删除成功')
      await loadCardList()
      await refreshStatistics()
    } catch (error) {
      console.error('删除卡密失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedCards.value.length === 0) {
    ElMessage.warning('请选择要删除的卡密')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedCards.value.length} 张卡密吗？`,
    '确认批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const ids = selectedCards.value.map((card: CardCode) => card.id)
      const response = await batchDeleteCardCodes(ids)

      if (response.success_count > 0) {
        ElMessage.success(`批量删除成功，成功删除 ${response.success_count} 张卡密`)
      }

      if (response.failed_count > 0) {
        ElMessage.warning(`有 ${response.failed_count} 张卡密删除失败`)
      }

      selectedCards.value = []
      await loadCardList()
      await refreshStatistics()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 创建卡密
const handleCreate = async (formData: CardCodeFormData) => {
  try {
    createLoading.value = true

    const response = await createCardCode(formData)
    console.log('创建卡密成功:', response)
    ElMessage.success('创建卡密成功')
    showCreateDialog.value = false

    // 刷新列表和统计
    await loadCardList()
    await refreshStatistics()
  } catch (error) {
    console.error('创建卡密失败:', error)
    ElMessage.error('创建卡密失败')
  } finally {
    createLoading.value = false
  }
}

// 处理创建对话框取消
const handleCreateCancel = () => {
  showCreateDialog.value = false
}

// 批量创建
const handleBatchCreate = async (formData: CardCodeBatchCreateData) => {
  try {
    batchCreateLoading.value = true

    const response = await batchCreateCardCodes(formData)
    console.log('批量创建卡密成功:', response)
    ElMessage.success(`批量创建成功，成功生成 ${response.success_count} 张卡密`)
    showBatchCreateDialog.value = false

    // 刷新列表和统计
    await loadCardList()
    await refreshStatistics()
  } catch (error) {
    console.error('批量创建卡密失败:', error)
    ElMessage.error('批量创建卡密失败')
  } finally {
    batchCreateLoading.value = false
  }
}

// 处理批量创建对话框取消
const handleBatchCreateCancel = () => {
  showBatchCreateDialog.value = false
}

// 刷新统计
const refreshStatistics = async (showTip: boolean = false) => {
  try {
    // 使用当前搜索条件进行统计
    const filters = {
      card_type: searchForm.card_type,
      status: searchForm.status,
      is_copied: searchForm.is_copied,
      copied_by: searchForm.copied_by,
      admin_id: searchForm.admin_id,
      time_type: searchForm.time_type,
      date_range: searchForm.date_range,
      start_date: searchForm.start_date,
      end_date: searchForm.end_date
    }

    const response = await getCardCodeStatistics(filters)
    Object.assign(statistics, response)
    console.log('统计数据刷新成功:', response)
    if (showTip) {
      ElMessage.success('统计数据已更新')
    }
  } catch (error) {
    console.error('刷新统计数据失败:', error)
    ElMessage.error('刷新统计数据失败')
  }
}

// 加载卡密列表
const loadCardList = async () => {
  loading.value = true
  try {
    const params: CardCodeListParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await getCardCodeList(params)
    cardList.value = response.list || []
    pagination.total = response.pagination?.total || 0
    pagination.totalPages = response.pagination?.totalPages || 0

    console.log('卡密列表加载成功:', response)
  } catch (error) {
    console.error('加载卡密列表失败:', error)
    ElMessage.error('加载卡密列表失败')
    cardList.value = []
  } finally {
    loading.value = false
  }
}

// 复制到剪贴板的函数
const copyToClipboard = (text: string, type: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success(`${type}已复制到剪贴板`)
  }).catch(err => {
    console.error('复制失败:', err)
    ElMessage.error(`复制${type}失败`)
  })
}

// 加载成员选项
const loadMemberOptions = async () => {
  try {
    const response = await getMemberOptions()
    memberOptions.value = response || []
    console.log('成员选项加载成功:', memberOptions.value)
  } catch (error) {
    console.error('加载成员选项失败:', error)
    memberOptions.value = []
  }
}

// 判断行是否可选择（已使用或已复制的不能选择）
const isRowSelectable = (row: CardCode) => {
  return row.status === 1 && row.is_copied === 0
}

// 组件挂载时加载数据
onMounted(() => {
  // 检查权限
  if (!hasCardAdminPermission.value) {
    console.warn('用户没有卡密管理权限')
    return
  }

  // 初始化默认时间范围
  const dateRange = getDateRange('thisMonth')
  searchForm.start_date = dateRange.start
  searchForm.end_date = dateRange.end
  searchForm.time_type = timeFilter.timeType
  searchForm.date_range = timeFilter.activeTab

  // 有权限才加载数据
  loadCardList()
  loadMemberOptions()
  refreshStatistics()
})
</script>

<style scoped>
.card-management {
  padding: 20px;
  /* 确保主容器不会裁剪下拉内容 */
  overflow: visible;
}

.no-permission {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 2px;
}

.stat-amount {
  font-size: 16px;
  font-weight: 500;
  color: #67C23A;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}



.time-type-selector {
  flex-shrink: 0;
}

.time-range-tabs {
  flex: 1;
  margin-bottom: 0;
}

.time-range-tabs :deep(.el-tabs__header) {
  margin: 0;
}

.time-range-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}



/* 时间筛选卡片样式 */
.time-filter-card {
  margin-bottom: 20px;
  /* 确保卡片不会裁剪下拉内容 */
  overflow: visible;
}

.time-filter-card :deep(.el-card__body) {
  /* 确保卡片内容区域不会裁剪下拉内容 */
  overflow: visible;
}

/* 时间筛选头部布局 */
.time-filter-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
  /* 确保头部区域不会裁剪下拉内容 */
  overflow: visible;
}

/* 自定义时间选择器样式 */
.time-filter-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-filter-actions .el-form {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-filter-actions .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}



.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .statistics-cards .el-col {
    margin-bottom: 15px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

/* 表格样式优化 */
.el-table {
  font-size: 14px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 对话框样式 */
.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

/* 状态标签样式 */
.el-tag {
  font-weight: 500;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 卡片阴影效果 */
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #EBEEF5;
}

.el-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

/* 统计卡片特殊样式 */
.stat-card .el-card__body {
  padding: 15px;
}

.stat-card:hover .stat-number {
  color: #67C23A;
  transition: color 0.3s ease;
}

/* 表单样式优化 */
.el-input-number {
  width: 100%;
}

.el-radio-group {
  width: 100%;
}

.el-radio {
  margin-right: 20px;
}

/* 加载状态样式 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.card-code-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  min-width: 0; /* 允许文本收缩 */
}

.card-code-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  cursor: pointer;
}

.card-code-cell .copy-button {
  flex-shrink: 0;
  margin-left: 5px;
  padding: 4px 8px;
  height: auto;
  min-height: unset;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.card-code-cell:hover .copy-button {
  opacity: 1;
}

/* 详情弹窗样式 */
.detail-content {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  min-height: 32px;
}

.detail-label {
  font-weight: 600;
  color: #303133;
  min-width: 80px;
  flex-shrink: 0;
  margin-right: 12px;
  line-height: 32px;
}

.detail-value {
  flex: 1;
  color: #606266;
  line-height: 32px;
  word-break: break-all;
}

.card-code-detail {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.card-code-full {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #409eff;
  word-break: break-all;
  line-height: 1.4;
}

.copy-btn-detail {
  flex-shrink: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.price-value {
  color: #e6a23c;
  font-weight: 600;
}

.points-value {
  color: #67c23a;
  font-weight: 600;
}

.vip-value {
  color: #409eff;
  font-weight: 600;
}

/* 使用信息和复制信息样式 */
.usage-info,
.copy-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.usage-user,
.copy-user {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.usage-time,
.copy-time {
  font-size: 12px;
  color: #909399;
}
</style>

<style>
/* 全局样式，不使用scoped */
.card-detail-dialog {
  border-radius: 8px;
}

.card-detail-dialog .el-dialog__header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-detail-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-detail-dialog .el-dialog__body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.card-detail-dialog .el-dialog__footer {
  padding: 10px 20px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* 表格中的tooltip样式优化 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}

/* 复制按钮hover效果 */
.copy-button:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

/* 卡密信息列样式 */
.card-info-cell {
  line-height: 1.4;
}

.card-code-line {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #303133;
  word-break: break-all;
  white-space: normal;
}

.card-type-line {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.batch-no-line {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 价值信息列样式 */
.value-info-cell {
  line-height: 1.4;
}

.price-line {
  font-weight: bold;
  color: #e6a23c;
}

.points-line {
  font-size: 12px;
  color: #67c23a;
  margin-top: 2px;
}

.vip-line {
  font-size: 12px;
  color: #409eff;
  margin-top: 2px;
}

/* 状态信息列样式 */
.status-info-cell {
  line-height: 1.4;
}

.status-line {
  margin-bottom: 4px;
}

.copy-status-line {
  margin-top: 4px;
}

.used-info-line,
.copied-info-line {
  font-size: 12px;
  color: #606266;
  margin-top: 2px;
}

.used-time-line,
.copied-time-line {
  font-size: 11px;
  color: #909399;
  margin-top: 1px;
}


</style>


