<?php

namespace App\Console\Commands;

use App\Service\System\VersionConfigService;
use Illuminate\Console\Command;

/**
 * 版本控制管理命令
 * 
 * 提供命令行工具来管理版本验证设置
 */
class VersionControlCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'version:control 
                            {action : 操作类型 (status|enable|disable|reset|summary)}
                            {--temp : 临时操作，不修改配置文件}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理版本验证控制设置';

    private VersionConfigService $versionConfigService;

    public function __construct(VersionConfigService $versionConfigService)
    {
        parent::__construct();
        $this->versionConfigService = $versionConfigService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');
        $isTemp = $this->option('temp');

        switch ($action) {
            case 'status':
                return $this->showStatus();
            
            case 'enable':
                return $this->enableValidation($isTemp);
            
            case 'disable':
                return $this->disableValidation($isTemp);
            
            case 'reset':
                return $this->resetValidation();
            
            case 'summary':
                return $this->showSummary();
            
            default:
                $this->error("未知操作: {$action}");
                $this->info('可用操作: status, enable, disable, reset, summary');
                return 1;
        }
    }

    /**
     * 显示当前版本验证状态
     */
    private function showStatus(): int
    {
        $enabled = $this->versionConfigService->isValidationEnabled();
        $strictMode = $this->versionConfigService->isStrictModeEnabled();
        
        $this->info('=== 版本验证状态 ===');
        $this->line("验证状态: " . ($enabled ? '<fg=green>启用</>' : '<fg=red>禁用</>'));
        $this->line("严格模式: " . ($strictMode ? '<fg=yellow>启用</>' : '<fg=gray>禁用</>'));
        
        if ($enabled) {
            $minVersion = $this->versionConfigService->getMinVersion();
            $maxVersion = $this->versionConfigService->getMaxVersion();
            $this->line("支持版本范围: <fg=cyan>{$minVersion}</> - <fg=cyan>{$maxVersion}</>");
        }
        
        return 0;
    }

    /**
     * 启用版本验证
     */
    private function enableValidation(bool $isTemp): int
    {
        if ($isTemp) {
            $this->versionConfigService->enableValidation();
            $this->info('✅ 版本验证已临时启用');
            $this->warn('注意: 这是临时设置，重启应用后将恢复配置文件设置');
        } else {
            $this->warn('永久启用需要修改 .env 文件中的 VERSION_VALIDATION_ENABLED=true');
            $this->info('当前已临时启用版本验证');
            $this->versionConfigService->enableValidation();
        }
        
        return 0;
    }

    /**
     * 禁用版本验证
     */
    private function disableValidation(bool $isTemp): int
    {
        if ($isTemp) {
            $this->versionConfigService->disableValidation();
            $this->info('⚠️  版本验证已临时禁用');
            $this->warn('注意: 这是临时设置，重启应用后将恢复配置文件设置');
        } else {
            $this->warn('永久禁用需要修改 .env 文件中的 VERSION_VALIDATION_ENABLED=false');
            $this->info('当前已临时禁用版本验证');
            $this->versionConfigService->disableValidation();
        }
        
        $this->error('警告: 禁用版本验证可能导致兼容性问题！');
        return 0;
    }

    /**
     * 重置验证状态
     */
    private function resetValidation(): int
    {
        $this->versionConfigService->resetValidationState();
        $this->info('✅ 版本验证状态已重置到配置文件设置');
        
        return $this->showStatus();
    }

    /**
     * 显示详细摘要
     */
    private function showSummary(): int
    {
        $summary = $this->versionConfigService->getVersionConfigSummary();
        
        $this->info('=== 版本控制配置摘要 ===');
        
        $headers = ['配置项', '当前值'];
        $rows = [
            ['最小支持版本', $summary['min_version']],
            ['最大支持版本', $summary['max_version']],
            ['最新版本', $summary['latest_version']],
            ['验证启用状态', $summary['validation_enabled'] ? '启用' : '禁用'],
            ['严格模式', $summary['strict_mode'] ? '启用' : '禁用'],
            ['缓存启用状态', $summary['cache_enabled'] ? '启用' : '禁用'],
            ['缓存TTL', $summary['cache_ttl'] . ' 秒'],
        ];
        
        $this->table($headers, $rows);
        
        // 显示使用建议
        $this->info('=== 使用建议 ===');
        if (!$summary['validation_enabled']) {
            $this->warn('• 版本验证已禁用，建议在生产环境中启用');
        }
        
        if (!$summary['strict_mode']) {
            $this->info('• 严格模式已禁用，系统将更宽松地处理版本验证');
        }
        
        $this->info('• 使用 php artisan version:control enable --temp 临时启用验证');
        $this->info('• 使用 php artisan version:control disable --temp 临时禁用验证');
        
        return 0;
    }
}