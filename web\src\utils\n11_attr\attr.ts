/**
 * N11分类属性常量定义
 * 用于存储各个分类ID对应的属性配置
 * 每个分类项以分类ID为key，值为包含id、valueId、customValue的属性数组
 */

// 分类属性接口定义
export interface CategoryAttributeConfig {
  id: number          // 属性ID
  valueId: number | null    // 属性值ID（如果使用预定义值）
  customValue: string | null // 自定义值（如果允许自定义）
}

// 分类配置映射类型
export type CategoryConfigMap = {
  [categoryId: number]: CategoryAttributeConfig[]
}

/**
 * N11分类属性配置常量
 * 根据分类ID获取对应的属性配置
 */
export const N11_CATEGORY_ATTRS: CategoryConfigMap = {
  // 分类ID: 1000491 - 手机壳 这个分类属性获取有60多M 实在太大 只能单独处理
  1000491: [
    {
      id: 1, // 品牌属性ID
      valueId: null,
      customValue: null
    },
    {
      id: 6770, // Seçenek
      valueId: null,
      customValue: null
    },
    {
      id: 631, // Uyumlu Model
      valueId: 8693570, //固定值 -
      customValue: null
    }
  ]
}

/**
 * 根据分类ID获取属性配置
 * @param categoryId 分类ID
 * @returns 属性配置数组，如果分类不存在则返回空数组
 */
export const getCategoryAttributes = (categoryId: number): CategoryAttributeConfig[] => {
  return N11_CATEGORY_ATTRS[categoryId] || []
}

/**
 * 检查分类ID是否存在配置
 * @param categoryId 分类ID
 * @returns 是否存在配置
 */
export const hasCategoryConfig = (categoryId: number): boolean => {
  return categoryId in N11_CATEGORY_ATTRS
}

/**
 * 获取所有已配置的分类ID列表
 * @returns 分类ID数组
 */
export const getAllConfiguredCategoryIds = (): number[] => {
  return Object.keys(N11_CATEGORY_ATTRS).map(Number)
}

/**
 * 根据分类ID和属性ID获取特定属性配置
 * @param categoryId 分类ID
 * @param attributeId 属性ID
 * @returns 属性配置，如果不存在则返回null
 */
export const getCategoryAttributeById = (
  categoryId: number,
  attributeId: number
): CategoryAttributeConfig | null => {
  const categoryAttrs = getCategoryAttributes(categoryId)
  return categoryAttrs.find(attr => attr.id === attributeId) || null
}
