/**
 * 积分管理API接口
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 积分记录接口定义
export interface PointsLog {
  id: number
  task_id: number
  points_before: number
  points_deducted: number
  points_after: number
  type: string
  description: string
  created_at: string
  task_info?: {
    id: number
    directory_id: number
    user_account_id: number
    task_over: number
  } | null
}

// 积分记录列表参数
export interface PointsLogsParams {
  page?: number
  page_size?: number
  start_date?: string  // YYYY-MM-DD格式
  end_date?: string    // YYYY-MM-DD格式
}

// 积分记录列表响应
export interface PointsLogsResponse {
  total: number
  page: number
  page_size: number
  data: PointsLog[]
}

/**
 * 获取用户积分使用记录
 */
export async function getUserPointsLogs(params: PointsLogsParams = {}): Promise<PointsLogsResponse> {
  const url = await getApiUrl('apiPointsLogsUrl')
  console.log('获取积分记录URL:', url)
  return sendRequestViaBackground({
    funName: 'getUserPointsLogs',
    url,
    method: 'get',
    params,
    auth: true
  })
}
