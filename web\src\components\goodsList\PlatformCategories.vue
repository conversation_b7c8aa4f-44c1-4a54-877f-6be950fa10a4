<template>
  <!-- 平台分类详情对话框 -->
  <el-dialog
    v-model="platformCategoriesDialogVisible"
    title="平台分类详情"
    width="700px"
  >
    <div class="platform-categories-detail" v-if="currentPlatformRelation">
      <div class="platform-info">
        <h3>
          <span :class="['category-type-badge', `category-type-badge--${currentPlatformRelation.cat_type}`]">
            {{ getCategoryTypeLabel(currentPlatformRelation.cat_type) }}
          </span>
        </h3>
      </div>
      <el-table :data="currentPlatformRelation.third_platform_categories" stripe :class="`table--${currentPlatformRelation.cat_type}`">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="分类名称" min-width="150">
          <template #default="{ row }">
            <div class="category-names-detail">
              <div class="name-cn">{{ row.name }}</div>
              <div class="name-tl">{{ row.name_tl }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分类路径" min-width="200">
          <template #default="{ row }">
            <div class="category-paths-detail">
              <div class="path-cn">{{ row.path_name }}</div>
              <div class="path-tl">{{ row.path_name_tl }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="platformCategoriesDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 关联商品分类对话框 -->
  <el-dialog
    v-model="categoryLinkDialogVisible"
    title="关联商品分类"
    width="80%"
    :close-on-click-modal="false"
    class="category-link-dialog"
  >
    <div v-if="currentGoodsForCategoryLink" class="goods-info-section">
      <h3>当前商品信息</h3>
      <div class="goods-info">
        <div class="goods-basic">
          <el-image
            :src="currentGoodsForCategoryLink.goods_thumb"
            fit="cover"
            style="width: 60px; height: 60px; border-radius: 4px; margin-right: 15px;"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="goods-details">
            <div class="goods-name">{{ currentGoodsForCategoryLink.goods_name }}</div>
            <div class="goods-meta">
              <span class="goods-id">商品ID: {{ currentGoodsForCategoryLink.goods_id }}</span>
              <span class="category-path">分类: {{ currentGoodsForCategoryLink.front_cat_2_path_name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ThirdPartyLinkage
      v-if="categoryLinkDialogVisible && currentTemuCategory"
      :temu-category="currentTemuCategory"
      :platforms="availablePlatforms"
      @close="categoryLinkDialogVisible = false"
      @success="handleCategoryLinkSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Picture } from '@element-plus/icons-vue'
import ThirdPartyLinkage from '../ThirdPartyLinkage.vue'
import { type Goods as GoodsType } from '../../utils/goodsApi'
import { type PlatformRelation, type Platform, type TemuCategory } from '../../types/platform'

// 定义接口 - 使用 GoodsType 作为基础类型
interface Goods extends GoodsType {
  // 可以在这里添加额外的属性
}

// Props
interface Props {
  platformCategoriesVisible: boolean
  categoryLinkVisible: boolean
  currentPlatformRelation: PlatformRelation | null
  currentGoodsForCategoryLink: Goods | null
  currentTemuCategory: TemuCategory | null
  availablePlatforms: Platform[]
}

const props = withDefaults(defineProps<Props>(), {
  platformCategoriesVisible: false,
  categoryLinkVisible: false,
  currentPlatformRelation: null,
  currentGoodsForCategoryLink: null,
  currentTemuCategory: null,
  availablePlatforms: () => []
})

// Events
const emit = defineEmits<{
  'update:platformCategoriesVisible': [value: boolean]
  'update:categoryLinkVisible': [value: boolean]
  categoryLinkSuccess: []
}>()

// 本地对话框状态
const platformCategoriesDialogVisible = ref(false)
const categoryLinkDialogVisible = ref(false)

// 监听 props 变化
watch(() => props.platformCategoriesVisible, (newValue) => {
  platformCategoriesDialogVisible.value = newValue
})

watch(() => props.categoryLinkVisible, (newValue) => {
  categoryLinkDialogVisible.value = newValue
})

// 监听本地对话框状态变化
watch(platformCategoriesDialogVisible, (newValue) => {
  emit('update:platformCategoriesVisible', newValue)
})

watch(categoryLinkDialogVisible, (newValue) => {
  emit('update:categoryLinkVisible', newValue)
})

// 获取分类类型标签
const getCategoryTypeLabel = (catType: string) => {
  const typeMap: Record<string, string> = {
    'system_cat': 'N11-类目',
    'user_cat': 'N11-用户关联'
  }
  return typeMap[catType] || ''
}

// 处理关联商品分类成功后的回调
const handleCategoryLinkSuccess = () => {
  emit('categoryLinkSuccess')
}
</script>

<style scoped>
/* 平台分类详情对话框样式 */
.platform-categories-detail {
  max-height: 500px;
  overflow-y: auto;
}

.platform-info h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-type-badge {
  font-size: 12px;
  font-weight: normal;
  padding: 4px 8px;
  border-radius: 12px;
  color: #fff;
}

.category-type-badge--system_cat {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.category-type-badge--user_cat {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.category-names-detail {
  line-height: 1.4;
}

.category-names-detail .name-cn {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 4px;
}

.category-names-detail .name-tl {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.category-paths-detail {
  line-height: 1.4;
}

.category-paths-detail .path-cn {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.category-paths-detail .path-tl {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

/* 系统分类表格样式 */
.table--system_cat {
  border: 2px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.table--system_cat .el-table__header {
  background: rgba(255, 107, 107, 0.1);
}

.table--system_cat .category-names-detail .name-cn {
  color: #c53030;
}

.table--system_cat .category-names-detail .name-tl {
  color: #e53e3e;
}

.table--system_cat .category-paths-detail .path-cn {
  color: #e53e3e;
}

.table--system_cat .category-paths-detail .path-tl {
  color: #fc8181;
}

/* 用户分类表格样式 */
.table--user_cat {
  border: 2px solid rgba(78, 205, 196, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.table--user_cat .el-table__header {
  background: rgba(78, 205, 196, 0.1);
}

.table--user_cat .category-names-detail .name-cn {
  color: #065f46;
}

.table--user_cat .category-names-detail .name-tl {
  color: #047857;
}

.table--user_cat .category-paths-detail .path-cn {
  color: #047857;
}

.table--user_cat .category-paths-detail .path-tl {
  color: #059669;
}

/* 商品分类关联对话框样式 */
.category-link-dialog {
  min-height: 600px;
}

.goods-info-section {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.goods-info-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.goods-basic {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-id {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  width: fit-content;
}

.category-path {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
