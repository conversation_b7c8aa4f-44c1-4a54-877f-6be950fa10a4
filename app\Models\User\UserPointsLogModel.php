<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPointsLogModel extends BaseModel
{
    protected $table = 'user_points_log';

    protected $fillable = [
        'user_id',
        'task_id',
        'points_before',
        'points_deducted',
        'points_after',
        'type',
        'description'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'task_id' => 'integer',
        'points_before' => 'integer',
        'points_deducted' => 'integer',
        'points_after' => 'integer',
        'type' => 'integer'
    ];

    // 关联用户
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 关联任务
    public function task(): BelongsTo
    {
        return $this->belongsTo(UserTaskModel::class, 'task_id', 'id');
    }

    // 作用域：按用户ID筛选
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    // 作用域：按任务ID筛选
    public function scopeByTaskId($query, int $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    // 作用域：按类型筛选
    public function scopeByType($query, int $type)
    {
        return $query->where('type', $type);
    }

    // 常量定义
    const TYPE_TASK_DEDUCTION = 1; // 任务扣除积分
}
