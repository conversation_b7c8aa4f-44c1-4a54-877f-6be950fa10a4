<?php
namespace App\Http\Middleware;

use App\Exceptions\MyException;
use Closure;
use Illuminate\Http\Request;

class DenySubAccount
{
    /**
     * 处理传入的请求，禁止子账号访问
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws \App\Exceptions\MyException 当用户是子账号时抛出异常
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取当前用户信息
        $user = $request->attributes->get('user');
        
        if (!$user) {
            throw new MyException('用户未登录', 401);
        }
        // 检查是否为子账号
        if (isset($user['pid']) && $user['pid'] > 0) {
            throw new MyException('您没有权限访问该功能');
        }
        return $next($request);
    }
}