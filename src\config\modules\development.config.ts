export const developmentConfig = {
  allowedDomains: ['test.com'],
  n11DebugMode: true,
  loginUrl: 'http://tsa.test.com/api/login/loginSms',
  registerUrl: 'http://tsa.test.com/api/user/register',
  captcha: 'http://tsa.test.com/api/captcha',
  temuGoodsAddUrl: 'http://tsa.test.com/api/temu/goodsAdd',
  temuCheckGoodsExistsUrl: 'http://tsa.test.com/api/temu/checkGoodsExists',
  temuBatchCheckGoodsExistsUrl: 'http://tsa.test.com/api/temu/batchCheckGoodsExists',
  userInfoUrl: 'http://tsa.test.com/api/user/info',
  userLogoutUrl: 'http://tsa.test.com/api/user/logout',
  storeListUrl: 'http://tsa.test.com/api/store/list',
  storeCreateUrl: 'http://tsa.test.com/api/store/create',
  storeUpdateUrl: 'http://tsa.test.com/api/store/update',
  storeDeleteUrl: 'http://tsa.test.com/api/store/delete',
  storeDetailUrl: 'http://tsa.test.com/api/store/detail',
  storeBatchUpdateUrl: 'http://tsa.test.com/api/store/batch-update',
  goodsListUrl: 'http://tsa.test.com/api/goods/list',
  goodsAddUrl: 'http://tsa.test.com/api/goods/add',
  goodsUpdateUrl: 'http://tsa.test.com/api/goods/update',
  goodsDeleteUrl: 'http://tsa.test.com/api/goods/delete',
  goodsDetailUrl: 'http://tsa.test.com/api/goods/detail',
  goodsBatchUpdateUrl: 'http://tsa.test.com/api/goods/batch-update',
  goodsAdjustSkuPriceUrl: 'http://tsa.test.com/api/goods/adjust-sku-price',
  goodsPriceAdjustmentLogsUrl: 'http://tsa.test.com/api/goods/price-adjustment-logs',
  catTemuWebpageMainUrl: 'http://tsa.test.com/api/goods_category/cat_temu_webpage_main',
  catTemuWebpageListUrl: 'http://tsa.test.com/api/goods_category/cat_temu_webpage_list',
  catTemuMainUrl: 'http://tsa.test.com/api/goods_category/cat_temu_main',
  catTemuListUrl: 'http://tsa.test.com/api/goods_category/cat_temu_list',
  catTemuListLazyUrl: 'http://tsa.test.com/api/goods_category/cat_temu_list_lazy',
  catTemuListflatUrl: 'http://tsa.test.com/api/goods_category/cat_temu_list_flat',
  catTemuChildrenCountUrl: 'http://tsa.test.com/api/goods_category/cat_temu_children_count',
  catTemuChildrenCountsUrl: 'http://tsa.test.com/api/goods_category/cat_temu_children_counts',
  catN11ListUrl: 'http://tsa.test.com/api/goods_category/cat_n11_list',
  catN11DetailUrl: 'http://tsa.test.com/api/goods_category/cat_n11_detail',
  catN11UpdateUrl: 'http://tsa.test.com/api/goods_category/cat_n11_update',
  catRelationSetUrl: 'http://tsa.test.com/api/goods_category/cat_relation_set',
  catRelationListUrl: 'http://tsa.test.com/api/goods_category/cat_relation_list',
  userStoreInfoUrl: 'http://tsa.test.com/api/user/store_info',
  taskAddUrl: 'http://tsa.test.com/api/user/task/add',
  taskListUrl: 'http://tsa.test.com/api/user/task/list',
  taskStartUrl: 'http://tsa.test.com/api/user/task/start',
  taskUpdateUrl: 'http://tsa.test.com/api/user/task/update',
  taskDetailUrl: 'http://tsa.test.com/api/user/task/detail',
  taskDetailListUrl: 'http://tsa.test.com/api/user/task/detail-list',
  taskQueryResultsUrl: 'http://tsa.test.com/api/user/task/detail/query-results',
  taskPendingQueryUrl: 'http://tsa.test.com/api/user/task/detail/pending-query',
  taskBatchUpdateUrl: 'http://tsa.test.com/api/user/task/detail/batch-update',
  taskSaveUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/save-upload-params',
  taskRetryUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/retry-upload-params',
  taskBatchRetryUploadUrl: 'http://tsa.test.com/api/user/task/detail/batch-retry-upload',
  taskBatchRetryUploadByStatusUrl: 'http://tsa.test.com/api/user/task/detail/batch-retry-upload-by-status',
  taskUpdateCategoryUrl: 'http://tsa.test.com/api/user/task/update-category',
  catTemuDetailUrl: 'http://tsa.test.com/api/goods_category/cat_temu_detail',
  goodsDirectoryListUrl: 'http://tsa.test.com/api/goods_directory/list',
  goodsDirectoryCreateUrl: 'http://tsa.test.com/api/goods_directory/create',
  goodsDirectoryUpdateUrl: 'http://tsa.test.com/api/goods_directory/update',
  goodsDirectoryDeleteUrl: 'http://tsa.test.com/api/goods_directory/delete',
  goodsDirectoryDetailUrl: 'http://tsa.test.com/api/goods_directory/detail',
  goodsDirectoryBatchUpdateUrl: 'http://tsa.test.com/api/goods_directory/batch-update',
  goodsDirectoryUpdateGoodsCountUrl: 'http://tsa.test.com/api/goods_directory/update-goods-count',
  goodsDirectoryUpdateAllGoodsCountUrl: 'http://tsa.test.com/api/goods_directory/update-all-goods-count',
  userCollectionSettingsUrl: 'http://tsa.test.com/api/user/collection-settings',
  userAvailableDirectoriesUrl: 'http://tsa.test.com/api/user/available-directories',
  userCheckNeedSetupUrl: 'http://tsa.test.com/api/user/check-need-setup',
  userProcessGoodsImagesUrl: 'http://tsa.test.com/api/user/process-goods-images',
  goodsNeedImageProcessUrl: 'http://tsa.test.com/api/goods/need-image-process',
  goodsStatisticsUrl: 'http://tsa.test.com/api/goods/statistics',
  goodsCatRelationGetUrl: 'http://tsa.test.com/api/goods_cat_relation/get',
  goodsCatRelationSaveUrl: 'http://tsa.test.com/api/goods_cat_relation/save',
  goodsCatRelationDeleteUrl: 'http://tsa.test.com/api/goods_cat_relation/delete',
  goodsCatRelationBatchGetUrl: 'http://tsa.test.com/api/goods_cat_relation/batch-get',
  kfUrl: 'http://tsa.test.com/qrcode/kf.png',
  cardCodeListUrl: 'http://tsa.test.com/api/card_code/list',
  cardCodeCreateUrl: 'http://tsa.test.com/api/card_code/create',
  cardCodeBatchCreateUrl: 'http://tsa.test.com/api/card_code/batch-create',
  cardCodeUpdateUrl: 'http://tsa.test.com/api/card_code/update',
  cardCodeDeleteUrl: 'http://tsa.test.com/api/card_code/delete',
  cardCodeBatchDeleteUrl: 'http://tsa.test.com/api/card_code/batch-delete',
  cardCodeDetailUrl: 'http://tsa.test.com/api/card_code/detail',
  cardCodeStatisticsUrl: 'http://tsa.test.com/api/card_code/statistics',
  cardCodeUseCardUrl: 'http://tsa.test.com/api/card_code/use-card',
  cardCodeUserUsageRecordsUrl: 'http://tsa.test.com/api/card_code/user-usage-records',
  cardCodeUsageRecordsUrl: 'http://tsa.test.com/api/card_code/usage-records',
  cardCodeCardTypeOptionsUrl: 'http://tsa.test.com/api/card_code/card-type-options',
  cardCodeStatusOptionsUrl: 'http://tsa.test.com/api/card_code/status-options',
  cardCodeCopyUrl: 'http://tsa.test.com/api/card_code/copy',
  cardCodeVipDaysUnitOptionsUrl: 'http://tsa.test.com/api/card_code/vip-days-unit-options',
  cardCodeCopyStatusOptionsUrl: 'http://tsa.test.com/api/card_code/copy-status-options',
  cardCodeMemberOptionsUrl: 'http://tsa.test.com/api/card_code/member-options',
  aiKeyListUrl: 'http://tsa.test.com/api/ai_key/list',
  aiKeyCreateUrl: 'http://tsa.test.com/api/ai_key/create',
  aiKeyUpdateUrl: 'http://tsa.test.com/api/ai_key/update',
  aiKeyDeleteUrl: 'http://tsa.test.com/api/ai_key/delete',
  aiKeyDetailUrl: 'http://tsa.test.com/api/ai_key/detail',
  aiKeyBatchUpdateUrl: 'http://tsa.test.com/api/ai_key/batch-update',
  n11RejectedProductBatchSaveUrl: 'http://tsa.test.com/api/n11/rejected-products/batch-save',
  n11RejectedProductPendingCountUrl: 'http://tsa.test.com/api/n11/rejected-products/pending-count',
  n11RejectedProductNextPendingUrl: 'http://tsa.test.com/api/n11/rejected-products/next-pending',
  n11RejectedProductUpdateStatusUrl: 'http://tsa.test.com/api/n11/rejected-products/update-status',
  n11RejectedProductBatchUpdateStatusUrl: 'http://tsa.test.com/api/n11/rejected-products/batch-update-status',
  n11RejectedProductStatisticsUrl: 'http://tsa.test.com/api/n11/rejected-products/statistics',
  taskNewGenerateDetailsUrl: 'http://tsa.test.com/api/user/task-new/generate-details',
  taskNewNextAiTaskUrl: 'http://tsa.test.com/api/user/task-new/next-ai-task',
  taskNewUpdateAiResultUrl: 'http://tsa.test.com/api/user/task-new/update-ai-result',
  taskNewRandomAiKeyUrl: 'http://tsa.test.com/api/user/task-new/random-ai-key',
  taskNewAiProgressUrl: 'http://tsa.test.com/api/user/task-new/ai-progress',
  taskNewCompleteAiTaskUrl: 'http://tsa.test.com/api/user/task-new/complete-ai-task',
  pointsLogsUrl: 'http://tsa.test.com/api/user/points/logs',
  pointsCheckTaskDeductedUrl: 'http://tsa.test.com/api/user/points/check-task-deducted',
  generateCredentialsUrl: 'http://tsa.test.com/api/user/generate-api-credentials',
  subAccountListUrl: 'http://tsa.test.com/api/sub-account/list',
  subAccountCreateUrl: 'http://tsa.test.com/api/sub-account/create',
  subAccountUpdateUrl: 'http://tsa.test.com/api/sub-account/update',
  subAccountDeleteUrl: 'http://tsa.test.com/api/sub-account/delete',
  subAccountToggleStatusUrl: 'http://tsa.test.com/api/sub-account/toggle-status',
  subAccountDetailUrl: 'http://tsa.test.com/api/sub-account/detail',
  subAccountGoodsListUrl: 'http://tsa.test.com/api/sub-account-goods/list',
  subAccountGoodsDeleteUrl: 'http://tsa.test.com/api/sub-account-goods/delete',
  changePasswordUrl: 'http://tsa.test.com/api/user/change-password',
};