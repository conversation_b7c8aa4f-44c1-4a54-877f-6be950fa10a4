/**
 * 商品复选框管理工具
 * 负责商品复选框的添加、移除和DOM监听
 */

/**
 * 为商品元素添加复选框
 * @param productElement 商品DOM元素
 */
export const addCheckboxToProduct = (productElement: Element): void => {
  // 避免重复添加
  if (productElement.querySelector('.product-checkbox')) {
    return;
  }

  // 查找商品图片容器
  const imageContainer = productElement.querySelector('.goods-image-container-external, ._6q6qVUF5._1QhQr8pq');
  if (!imageContainer) {
    return;
  }

  // 创建复选框容器
  const checkboxContainer = document.createElement('div');
  checkboxContainer.className = 'product-checkbox';
  checkboxContainer.innerHTML = `
    <input type="checkbox" class="checkbox-input" checked />
    <div class="checkbox-visual">
      <svg width="12" height="12" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="checkbox-icon">
        <path d="M13 4L6 11L3 8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  `;

  // 设置样式
  checkboxContainer.style.cssText = `
    position: absolute;
    top: 6px;
    left: 6px;
    z-index: 1000;
    cursor: pointer;
    width: 16px;
    height: 16px;
  `;

  // 复选框输入框样式
  const checkboxInput = checkboxContainer.querySelector('.checkbox-input') as HTMLInputElement;
  if (checkboxInput) {
    checkboxInput.style.cssText = `display: none;`;
  }

  // 复选框视觉元素样式
  const checkboxVisual = checkboxContainer.querySelector('.checkbox-visual') as HTMLElement;
  if (checkboxVisual) {
    checkboxVisual.style.cssText = `
      width: 16px;
      height: 16px;
      border: 2px solid #007AFF;
      border-radius: 3px;
      background: #007AFF;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.2s ease;
    `;
  }

  // 添加点击事件
  checkboxContainer.addEventListener('click', (e) => {
    e.stopPropagation();
    e.preventDefault();

    if (checkboxInput && checkboxVisual) {
      checkboxInput.checked = !checkboxInput.checked;
      const checkboxIcon = checkboxVisual.querySelector('.checkbox-icon') as HTMLElement;

      if (checkboxInput.checked) {
        checkboxVisual.style.background = '#007AFF';
        checkboxVisual.style.borderColor = '#007AFF';
        if (checkboxIcon) {
          checkboxIcon.style.display = 'block';
        }
      } else {
        checkboxVisual.style.background = 'transparent';
        checkboxVisual.style.borderColor = '#007AFF';
        if (checkboxIcon) {
          checkboxIcon.style.display = 'none';
        }
      }
    }
  });

  // 将复选框添加到图片容器
  imageContainer.appendChild(checkboxContainer);
};

/**
 * 为现有商品添加复选框
 */
export const addCheckboxesToExistingProducts = (): void => {
  const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
  if (!productsContainer) {
    console.log('未找到商品容器');
    return;
  }

  // 获取所有直接子节点
  const productElements = productsContainer.children;
  console.log('找到', productElements.length, '个商品元素');

  for (let i = 0; i < productElements.length; i++) {
    const productElement = productElements[i];
    addCheckboxToProduct(productElement);
  }
};

/**
 * 设置DOM监听器，监听新添加的商品
 * @returns 监听器实例
 */
export const setupDOMObserver = (): MutationObserver | null => {
  const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
  if (!productsContainer) {
    console.log('未找到商品容器，无法设置监听器');
    return null;
  }

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 为新添加的子节点添加复选框
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            addCheckboxToProduct(node as Element);
          }
        });
      }
    });
  });

  // 开始监听
  observer.observe(productsContainer, {
    childList: true,
    subtree: false
  });

  console.log('DOM监听器已设置');
  return observer;
};

/**
 * 停止DOM监听器
 * @param observer 监听器实例
 */
export const stopDOMObserver = (observer: MutationObserver | null): void => {
  if (observer) {
    observer.disconnect();
    console.log('DOM监听器已停止');
  }
};

/**
 * 移除所有复选框
 */
export const removeAllCheckboxes = (): void => {
  const checkboxes = document.querySelectorAll('.product-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.remove();
  });
  console.log('已移除所有复选框');
};

/**
 * 标记商品为已采集
 * @param productElement 商品DOM元素
 */
export const markProductAsCollected = (productElement: Element): void => {
  productElement.setAttribute('data-collected', 'true');
  
  // 更新复选框样式为已采集状态
  const checkboxContainer = productElement.querySelector('.product-checkbox');
  if (checkboxContainer) {
    const checkboxVisual = checkboxContainer.querySelector('.checkbox-visual') as HTMLElement;
    if (checkboxVisual) {
      checkboxVisual.style.background = '#28a745';
      checkboxVisual.style.borderColor = '#28a745';
      checkboxVisual.style.opacity = '0.7';
    }
    
    // 禁用复选框
    const checkboxInput = checkboxContainer.querySelector('.checkbox-input') as HTMLInputElement;
    if (checkboxInput) {
      checkboxInput.disabled = true;
    }
  }
};

/**
 * 重置所有商品的采集标记
 */
export const resetCollectionMarks = (): void => {
  const collectedProducts = document.querySelectorAll('[data-collected="true"]');
  collectedProducts.forEach(product => {
    product.removeAttribute('data-collected');
    
    // 恢复复选框样式
    const checkboxContainer = product.querySelector('.product-checkbox');
    if (checkboxContainer) {
      const checkboxVisual = checkboxContainer.querySelector('.checkbox-visual') as HTMLElement;
      if (checkboxVisual) {
        checkboxVisual.style.background = '#007AFF';
        checkboxVisual.style.borderColor = '#007AFF';
        checkboxVisual.style.opacity = '1';
      }
      
      // 启用复选框
      const checkboxInput = checkboxContainer.querySelector('.checkbox-input') as HTMLInputElement;
      if (checkboxInput) {
        checkboxInput.disabled = false;
      }
    }
  });
};

/**
 * 获取选中的商品链接（过滤已采集的商品）
 * @returns 选中的商品链接数组
 */
export const getSelectedProductLinks = (): string[] => {
  const selectedLinks: string[] = [];
  const checkboxes = document.querySelectorAll('.product-checkbox .checkbox-input:checked');

  checkboxes.forEach(checkbox => {
    const productElement = checkbox.closest('.EKDT7a3v') || checkbox.closest('[class*="EKDT7a3v"]');
    if (productElement && !productElement.hasAttribute('data-collected')) {
      const linkElement = productElement.querySelector('a[href]') as HTMLAnchorElement;
      if (linkElement && linkElement.href) {
        // 如果链接是相对路径，添加完整域名
        const fullLink = linkElement.href.startsWith('http')
          ? linkElement.href
          : `https://www.temu.com${linkElement.getAttribute('href')}`;
        selectedLinks.push(fullLink);
      }
    }
  });

  return selectedLinks;
};

/**
 * 获取商品总数
 * @returns 商品总数
 */
export const getTotalProductCount = (): number => {
  const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
  if (!productsContainer) {
    return 0;
  }
  return productsContainer.children.length;
};