/**
 * N11 被拒绝商品 API 请求文件
 */

import config from '@/config'

/**
 * API 响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

/**
 * 批量更新商品状态为拒绝状态
 * @param stockCodes stock_code 数组
 */
export async function batchUpdateRejectedStatus(stockCodes: string[]): Promise<ApiResponse> {
  return new Promise((resolve) => {
    try {
      console.log('开始调用后端接口批量更新商品状态...', { stockCodes })

      // 通过 background 转发请求
      chrome.runtime.sendMessage({
        funType: 'axios',
        url: config.apiN11RejectedProductBatchUpdateStatusUrl,
        method: 'post',
        headers: {
          'Content-Type': 'application/json'
        },
        pramas: {
          stock_codes: stockCodes
        },
        auth: true,
        encrypto: true
      }, (response: any) => {
        if (chrome.runtime.lastError) {
          console.error('Background请求失败:', chrome.runtime.lastError)
          resolve({
            success: false,
            message: `Background请求失败: ${chrome.runtime.lastError.message}`
          })
          return
        }

        console.log('后端接口响应:', response)

        if (response && response[0] && response[0].data) {
          const data = response[0].data;
          if(data.code == 1 && data.status == 200){
            resolve({
              success: true,
              message: response.message || '批量更新商品状态成功',
              data: response.data
            })
          }else{
            console.error('后端接口响应:', data)
            resolve({
              success: false,
              message: data.msg || '批量更新商品状态失败'
            })
          }
        } else {
          resolve({
            success: false,
            message: response?.message || '批量更新商品状态失败'
          })
        }
      })

    } catch (error: any) {
      console.error('调用后端接口时发生异常:', error)
      resolve({
        success: false,
        message: `调用后端接口异常: ${error.message}`
      })
    }
  })
}
