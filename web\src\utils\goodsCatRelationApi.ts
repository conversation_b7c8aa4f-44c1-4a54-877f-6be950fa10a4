/**
 * 商品分类关联API
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl, API_URLS } from './apiConfig'

// 商品分类关联数据接口
export interface GoodsCatRelation {
  id: number
  user_id: number
  goods_id: number
  temu_cat_id: number
  third_platform_id: number
  third_platform_cat_id: number
  third_platform_cat_name: string
  third_platform_cat_name_tl?: string
  third_platform_cat_path?: string
  third_platform_cat_path_tl?: string
  status: number
  created_at: string
  updated_at: string
}

// 保存商品分类关联的参数接口
export interface SaveGoodsCatRelationParams {
  goods_id: number
  temu_cat_id: number
  third_platform_id: number
  third_platform_cat_id: number
  third_platform_cat_name: string
  third_platform_cat_name_tl?: string
  third_platform_cat_path?: string
  third_platform_cat_path_tl?: string
}

// 获取商品分类关联的参数接口
export interface GetGoodsCatRelationParams {
  goods_id: number
  third_platform_id?: number
}

// 删除商品分类关联的参数接口
export interface DeleteGoodsCatRelationParams {
  goods_id: number
  third_platform_id?: number
}

// 批量获取商品分类关联的参数接口
export interface BatchGetGoodsCatRelationsParams {
  goods_ids: number[]
  third_platform_id?: number
}

/**
 * 获取商品分类关联
 * @param params 查询参数
 * @returns Promise<GoodsCatRelation | null>
 */
export const getGoodsCatRelation = async (params: GetGoodsCatRelationParams): Promise<GoodsCatRelation | null> => {
  const url = await getApiUrl(API_URLS.GOODS_CAT_RELATION_GET)
  
  const response = await sendRequestViaBackground({
    funName: 'request',
    url,
    method: 'GET',
    params,
    auth: true
  })

  return response?.data || null
}

/**
 * 保存商品分类关联
 * @param params 保存参数
 * @returns Promise<any>
 */
export const saveGoodsCatRelation = async (params: SaveGoodsCatRelationParams): Promise<any> => {
  const url = await getApiUrl(API_URLS.GOODS_CAT_RELATION_SAVE)
  
  const response = await sendRequestViaBackground({
    funName: 'request',
    url,
    method: 'POST',
    data: params,
    auth: true
  })

  return response
}

/**
 * 删除商品分类关联
 * @param params 删除参数
 * @returns Promise<any>
 */
export const deleteGoodsCatRelation = async (params: DeleteGoodsCatRelationParams): Promise<any> => {
  const url = await getApiUrl(API_URLS.GOODS_CAT_RELATION_DELETE)
  
  const response = await sendRequestViaBackground({
    funName: 'request',
    url,
    method: 'POST',
    data: params,
    auth: true
  })

  return response
}

/**
 * 批量获取商品分类关联
 * @param params 批量查询参数
 * @returns Promise<Record<number, GoodsCatRelation>>
 */
export const batchGetGoodsCatRelations = async (params: BatchGetGoodsCatRelationsParams): Promise<Record<number, GoodsCatRelation>> => {
  const url = await getApiUrl(API_URLS.GOODS_CAT_RELATION_BATCH_GET)
  
  const response = await sendRequestViaBackground({
    funName: 'request',
    url,
    method: 'POST',
    data: params,
    auth: true
  })

  return response?.data || {}
}

/**
 * 根据商品ID和TEMU分类信息获取当前TEMU分类详情
 * @param catId TEMU分类ID
 * @returns Promise<any>
 */
export const getTemuCategoryDetail = async (catId: number): Promise<any> => {
  // 这里可以调用现有的分类详情API
  // 暂时返回基本信息，实际应该调用分类详情接口
  return {
    id: catId,
    name: '',
    path_name: ''
  }
}
