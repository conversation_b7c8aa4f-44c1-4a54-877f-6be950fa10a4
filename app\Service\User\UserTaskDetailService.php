<?php

namespace App\Service\User;

use App\Utils\Jwt\Jwt;
use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\UserTaskModel;
use App\Models\User\UserTaskDetailModel;
use App\Models\User\UserTaskDetailUpParamsModel;
use App\Models\User\UserAccountModel;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\N11\ProductCategoryN11Model;
use App\Utils\GoodsNameTrait;
use App\Utils\GoodsNameAiRewriter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserTaskDetailService extends BaseService
{
    use GoodsNameTrait;
    
    protected UserTaskModel $userTaskModel;
    protected UserTaskDetailModel $userTaskDetailModel;
    protected UserTaskDetailUpParamsModel $userTaskDetailUpParamsModel;
    protected UserAccountModel $userAccountModel;
    protected GoodsModel $goodsModel;
    protected GoodsSkuModel $goodsSkuModel;
    protected ProductCategoryN11Model $productCategoryN11Model;
    protected GoodsNameAiRewriter $goodsNameAiRewriter;
    public function __construct(
        Jwt $jwtService,
        UserTaskModel $userTaskModel,
        UserTaskDetailModel $userTaskDetailModel,
        UserTaskDetailUpParamsModel $userTaskDetailUpParamsModel,
        UserAccountModel $userAccountModel,
        GoodsModel $goodsModel,
        GoodsSkuModel $goodsSkuModel,
        ProductCategoryN11Model $productCategoryN11Model,
        GoodsNameAiRewriter $goodsNameAiRewriter
    ) {
        $this->userTaskModel = $userTaskModel;
        $this->userTaskDetailModel = $userTaskDetailModel;
        $this->userTaskDetailUpParamsModel = $userTaskDetailUpParamsModel;
        $this->userAccountModel = $userAccountModel;
        $this->goodsModel = $goodsModel;
        $this->goodsSkuModel = $goodsSkuModel;
        $this->productCategoryN11Model = $productCategoryN11Model;
        $this->goodsNameAiRewriter = $goodsNameAiRewriter;
        parent::__construct($jwtService);
    }

    /**
     * 获取任务详情
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @return array
     * @throws MyException
     */
    public function getTaskDetail(int $userId, int $taskId): array
    {
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->with(['store', 'directory'])
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 获取任务统计信息
        $statistics = $this->getTaskDetailStatistics($userId, $taskId);

        return [
            'task_info' => $this->formatTaskData($task),
            'statistics' => $statistics
        ];
    }

    /**
     * 获取任务详情统计信息
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @return array
     * @throws MyException
     */
    public function getTaskDetailStatistics(int $userId, int $taskId): array
    {
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        $totalCount = $this->userTaskDetailModel->where('task_id', $taskId)->count();
        $successCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 1)->count();
        $failedCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 3)->count();
        //$failedCountRetry = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 3)->where('third_task_id', '>', '0')->count();//专门计算失败可以重试的任务数量
        $pendingCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 0)->count();
        $processingCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 2)->count();
        $existCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 4)->count();
        $noCategoryCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 5)->count();
        $rejectedCount = $this->userTaskDetailModel->where('task_id', $taskId)->where('status', 6)->count();//审核未通过

        return [
            'total_count' => $totalCount,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'failed_count_retry' => $failedCount,
            'pending_count' => $pendingCount,
            'processing_count' => $processingCount,
            'exist_count' => $existCount,
            'rejected_count' => $rejectedCount,
            'no_category_count' => $noCategoryCount,
            'success_rate' => $totalCount > 0 ? round(($successCount / $totalCount) * 100, 2) : 0
        ];
    }

    /**
     * 获取任务详情列表（分页）
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array
     * @throws MyException
     */
    public function getTaskDetailList(int $userId, array $params): array
    {
        $taskId = $params['task_id'];
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 20)));
        
        // 构建查询
        $query = $this->userTaskDetailModel->where('task_id', $taskId)
            ->with(['goods', 'goodsSku', 'store']);

        // 按状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', (int)$params['status']);
        }

        // 按修改分类状态筛选
        if (isset($params['is_cat_modified']) && $params['is_cat_modified'] !== '') {
            $query->where('is_cat_modified', (int)$params['is_cat_modified']);
        }

        // 按商品名称筛选
        if (!empty($params['goods_name'])) {
            $query->where('goods_name', 'like', '%' . $params['goods_name'] . '%');
        }

        // 按时间范围筛选 - 支持创建时间和更新时间
        $timeField = $params['sort_field'] ?? 'created_at';
        if (!empty($params['created_at_start'])) {
            $query->whereDate($timeField, '>=', $params['created_at_start']);
        }
        if (!empty($params['created_at_end'])) {
            $query->whereDate($timeField, '<=', $params['created_at_end']);
        }

        // 按任务详情ID筛选
        if (!empty($params['task_detail_id'])) {
            $query->where('id', (int)$params['task_detail_id']);
        }

        // 排序处理
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        
        // 验证排序字段
        if (!in_array($sortField, ['created_at', 'updated_at'])) {
            $sortField = 'created_at';
        }
        
        // 验证排序方式
        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }
        
        $query->orderBy($sortField, $sortOrder);

        $query->orderBy('id', 'desc');

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $taskDetails = $query->offset($offset)
            ->limit($pageSize)
            ->get()
            ->map(function($detail) {
                return $this->formatTaskDetailListData($detail);
            });

        return [
            'list' => $taskDetails,
            'pagination' => [
                'current' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 批量重试失败的任务详情
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param array $detailIds 任务详情ID数组
     * @return array
     * @throws MyException
     */
    public function batchRetryFailedTasks(int $userId, int $taskId, array $detailIds): array
    {
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($detailIds)){
            throw new MyException('任务详情ID不能为空');
        }
        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 验证任务详情是否属于该任务
        $validDetails = $this->userTaskDetailModel
            ->where('task_id', $taskId)
            ->where('user_id', $userId)
            ->whereIn('id', $detailIds)
            ->whereIn('status', [3, 5]) // 只能重试失败的和没有分类的
            ->get();

        if ($validDetails->isEmpty()) {
            throw new MyException('没有找到可重试的任务详情');
        }

        $retryCount = 0;
        DB::beginTransaction();
        
        try {
            foreach ($validDetails as $detail) {
                // 重置状态为等待上传
                $detail->update([
                    'status' => 0,
                    'third_task_id' => 0,
                    'third_type' => '',
                    'third_status' => '',
                    'third_result' => '',
                    'memo' => '批量重试 - ' . date('Y-m-d H:i:s'),
                    'updated_at' => now()
                ]);
                $retryCount++;
            }
            
            DB::commit();
            return [
                'retry_count' => $retryCount,
                'total_requested' => count($detailIds)
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('批量重试操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取任务查询统计信息（前端查询用）
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @return array
     * @throws MyException
     */
    public function queryTaskResults(int $userId, int $taskId): array
    {
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }

        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->with('store')
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        if (!$task->store) {
            throw new MyException('任务关联的店铺不存在');
        }

        // 获取店铺的N11 API配置
        $appKey = $task->store->app_key;
        $appSecret = $task->store->app_secret;
        
        if (empty($appKey) || empty($appSecret)) {
            throw new MyException('店铺N11 API配置不完整');
        }

        // 统计待查询的任务数量
        $totalPending = $this->userTaskDetailModel
            ->where('task_id', $taskId)
            ->where('user_id', $userId)
            ->where('status', 2) // 已上传待查询结果
            ->where('third_task_id', '>', 0)
            ->count();

        return [
            'total_pending' => $totalPending,
            'app_key' => $appKey,
            'app_secret' => $appSecret,
            'message' => "找到 {$totalPending} 条待查询记录，请在前端执行查询"
        ];
    }

    /**
     * 获取待查询结果的任务列表
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param int $limit 每次获取的数量限制
     * @param int $offset 偏移量
     * @return array
     * @throws MyException
     */
    public function getPendingQueryTasks(int $userId, int $taskId, int $limit = 500, int $offset = 0): array
    {
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }

        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->with('store')
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        if (!$task->store) {
            throw new MyException('任务关联的店铺不存在');
        }

        // 获取店铺的N11 API配置
        $appKey = $task->store->app_key;
        $appSecret = $task->store->app_secret;
        
        if (empty($appKey) || empty($appSecret)) {
            throw new MyException('店铺N11 API配置不完整');
        }

        // 获取待查询的任务详情
        $pendingDetails = $this->userTaskDetailModel
            ->where('task_id', $taskId)
            ->where('user_id', $userId)
            ->where('status', 2) // 已上传待查询结果
            ->where('third_task_id', '>', 0)
            ->offset($offset)
            ->limit($limit)
            ->get();

        // 按third_task_id分组
        $taskGroups = $pendingDetails->groupBy('third_task_id');
        $result = [];

        foreach ($taskGroups as $thirdTaskId => $details) {
            $result[] = [
                'third_task_id' => $thirdTaskId,
                'app_key' => $appKey,
                'app_secret' => $appSecret,
                'details' => $details->map(function($detail) {
                    return [
                        'id' => $detail->id,
                        'stock_code' => $detail->stock_code,
                        'goods_name' => $detail->goods_name
                    ];
                })->toArray()
            ];
        }

        return [
            'task_groups' => $result,
            'has_more' => $pendingDetails->count() == $limit,
            'total_count' => $pendingDetails->count()
        ];
    }

    /**
     * 批量更新任务详情状态
     * @param int $userId 用户ID
     * @param array $updates 更新数据数组
     * @return array
     * @throws MyException
     */
    public function batchUpdateTaskDetails(int $userId, array $updates): array
    {
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($updates)){
            throw new MyException('更新数据不能为空');
        }

        $updatedCount = 0;
        DB::beginTransaction();
        
        try {
            foreach ($updates as $update) {
                $detailId = $update['detail_id'] ?? 0;
                $status = $update['status'] ?? 0;
                $thirdStatus = $update['third_status'] ?? '';
                $thirdResult = $update['third_result'] ?? '';
                $memo = $update['memo'] ?? '';

                if ($detailId > 0) {
                    $detail = $this->userTaskDetailModel
                        ->where('id', $detailId)
                        ->where('user_id', $userId)
                        ->first();

                    if ($detail) {
                        $detail->update([
                            'status' => $status,
                            'third_status' => $thirdStatus,
                            'third_result' => $thirdResult,
                            'memo' => $memo . ' - ' . date('Y-m-d H:i:s'),
                            'updated_at' => now()
                        ]);
                        $updatedCount++;
                    }
                }
            }
            
            DB::commit();
            return [
                'updated_count' => $updatedCount,
                'message' => "成功更新 {$updatedCount} 条记录"
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('批量更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存上传参数
     * @param int $userId 用户ID
     * @param int $taskDetailId 任务详情ID
     * @param string $thirdType 第三方类型
     * @param string $params 参数JSON字符串
     * @return array
     * @throws MyException
     */
    public function saveUploadParams(int $userId, int $taskDetailId, string $thirdType, string $params): array
    {
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($taskDetailId)){
            throw new MyException('任务详情ID不能为空');
        }

        // 验证任务详情是否属于当前用户
        $detail = $this->userTaskDetailModel
            ->where('id', $taskDetailId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$detail) {
            throw new MyException('任务详情不存在');
        }

        try {
            // 创建新的参数记录 或 更新已存在的参数记录
            $upParams = $this->userTaskDetailUpParamsModel->updateOrCreate(
                ['task_detail_id' => $taskDetailId],
                ['third_type' => $thirdType, 'params' => $params]
            );

            return [
                'id' => $upParams->id,
                'task_detail_id' => $taskDetailId,
                'third_type' => $thirdType,
                'message' => '上传参数保存成功'
            ];
            
        } catch (\Exception $e) {
            throw new MyException('保存上传参数失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取重新上传参数
     * @param int $userId 用户ID
     * @param int $taskDetailId 任务详情ID
     * @return array
     * @throws MyException
     */
    public function getRetryUploadParams(int $userId, int $taskDetailId): array
    {
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($taskDetailId)){
            throw new MyException('任务详情ID不能为空');
        }

        // 获取任务详情及相关数据
        $detail = $this->userTaskDetailModel
            ->where('id', $taskDetailId)
            ->where('user_id', $userId)
            ->with(['goods', 'goodsSku', 'store'])
            ->first();
            
        if (!$detail) {
            throw new MyException('任务详情不存在');
        }

        // 获取任务信息
        $task = $this->userTaskModel
            ->where('id', $detail->task_id)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 验证分类关联是否有效
        if ($detail->category_id <= 0) {
            throw new MyException('商品没有关联N11分类，无法重新上传');
        }

        $n11Category = $this->productCategoryN11Model->find($detail->category_id);
        if (!$n11Category) {
            throw new MyException('N11分类不存在，请重新关联分类');
        }

        // 重新计算 quantity, vat_rate, preparing_day
        $quantity = $detail->quantity;
        $vatRate = $detail->vat_rate;
        $preparingDay = $detail->preparing_day;

        if ($task->account_setting_type == 1 && $detail->store) {
            // 使用店铺设置
            $quantity = $detail->store->quantity ?? $quantity;
            $vatRate = $detail->store->vat_rate ?? $vatRate;
            $preparingDay = $detail->store->preparing_day ?? $preparingDay;
        } else {
            // 使用任务设置
            $quantity = $task->quantity ?? $quantity;
            $vatRate = $task->vat_rate ?? $vatRate;
            $preparingDay = $task->preparing_day ?? $preparingDay;
        }

        $goods_name = $detail->goods_name_ai;
        if(empty($goods_name)){
            throw new MyException('商品名称不存在');
        }
        $goods_property = $detail->goods_property_ai;
        if(empty($goods_property)){
            $goods_property = $detail->goods_property;
        }

        // 构建返回数据，保持和原任务流程一致的结构
        return [
            'id' => $detail->id,
            'task_id' => $detail->task_id,
            'user_id' => $detail->user_id,
            'user_account_id' => $detail->user_account_id,
            'user_goods_id' => $detail->user_goods_id,
            'user_goods_sku_id' => $detail->user_goods_sku_id,
            'goods_id' => $detail->goods_id,
            'goods_name' => $goods_name,
            'thumb_url' => $detail->thumb_url,
            'images' => $this->getProductImages($detail),
            'images_detail' => [],//暂时不使用
            'currentcy' => $detail->currentcy,
            'currentcy_goods' => $detail->currentcy_goods,
            'price' => $detail->price,
            'price_third' => $detail->price_third,
            'price_list_third' => $detail->price_list_third,
            'quantity' => $quantity,
            'vat_rate' => $vatRate,
            'preparing_day' => $preparingDay,
            'spec_key_values' => $detail->spec_key_values,
            'is_skc_gallery' => $detail->is_skc_gallery,
            'category_id' => $detail->category_id,
            'product_main_id' => $detail->product_main_id,
            'stock_code' => $detail->stock_code,
            'status' => $detail->status,
            'goods_info' => $detail->goods ? [
                'id' => $detail->goods->id,
                'goods_name' => $goods_name,
                'goods_property' => $goods_property,
                'front_cat_id_2' => $detail->goods->front_cat_id_2 ?? 0
            ] : null,
            'sku_info' => $detail->goodsSku ? [
                'id' => $detail->goodsSku->id,
                'sku_id' => $detail->goodsSku->sku_id,
                'url' => $detail->goodsSku->url ?? '',
                'skc_gallery' => $detail->goodsSku->skc_gallery ?? ''
            ] : null,
            'store_info' => $detail->store ? [
                'id' => $detail->store->id,
                'account_name' => $detail->store->account_name,
                'account_type' => $detail->store->account_type,
                'brand' => $detail->store->brand ?? '',
                'price_rate' => $detail->store->price_rate ?? 1.0,
                'shipment_template' => $detail->store->shipment_template ?? '',
                'quantity' => $quantity,
                'vat_rate' => $vatRate,
                'preparing_day' => $preparingDay,
                'integrator_name' => $detail->store->integrator_name ?? '',
                'app_key' => $detail->store->app_key ?? '',
                'app_secret' => $detail->store->app_secret ?? ''
            ] : null,
            'created_at' => $detail->created_at,
            'updated_at' => $detail->updated_at,
            'task_count' => 1,
            'task_num' => 0,
            'store_name' => $detail->store ? $detail->store->account_name : '',
            'goods_sku_name' => $detail->spec_values ?? ''
        ];
    }

    /**
     * 批量重新上传失败任务
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param array $detailIds 任务详情ID数组，为空则处理所有失败任务
     * @return array
     * @throws MyException
     */
    public function batchRetryUpload(int $userId, int $taskId, array $detailIds = []): array
    {
        return $this->batchRetryUploadByStatus($userId, $taskId, [3], $detailIds);
    }

    /**
     * 按状态批量重新上传任务
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param array $statusList 状态列表，如 [3] 表示失败，[6] 表示审核未通过，[3,6] 表示失败和审核未通过
     * @param array $detailIds 任务详情ID数组，为空则处理所有指定状态的任务
     * @return array
     * @throws MyException
     */
    public function batchRetryUploadByStatus(int $userId, int $taskId, array $statusList, array $detailIds = []): array
    {
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($taskId)){
            throw new MyException('任务ID不能为空');
        }
        // 验证状态列表
        if(empty($statusList)){
            throw new MyException('状态列表不能为空');
        }
        if(!is_array($statusList)){
            throw new MyException('状态列表必须是数组');
        }
        
        // 过滤并验证状态列表，只保留大于0的数字
        $statusList = array_filter($statusList, function($status) {
            return is_numeric($status) && intval($status) > 0;
        });
        
        // 转换为整数数组
        $statusList = array_map('intval', $statusList);
        
        if(empty($statusList)){
            throw new MyException('状态列表中没有有效的状态值（必须是大于0的数字）');
        }

        // 验证任务是否属于当前用户
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 构建查询条件
        $query = $this->userTaskDetailModel
            ->where('task_id', $taskId)
            ->where('user_id', $userId);
            
        // 优化状态查询：单个状态用where，多个状态用whereIn
        if (count($statusList) === 1) {
            $query->where('status', $statusList[0]); // 单个状态直接用等于判断，效率更高
        } else {
            $query->whereIn('status', $statusList); // 多个状态使用whereIn
        }

        if (!empty($detailIds)) {
            $query->whereIn('id', $detailIds);
        }

        $targetDetails = $query->with(['goods', 'goodsSku', 'store'])->orderBy('id', 'desc')->get();

        if ($targetDetails->isEmpty()) {
            $statusNames = [];
            foreach ($statusList as $status) {
                switch ($status) {
                    case 3:
                        $statusNames[] = '失败';
                        break;
                    case 6:
                        $statusNames[] = '审核未通过';
                        break;
                    default:
                        $statusNames[] = "状态{$status}";
                        break;
                }
            }
            $statusText = implode('和', $statusNames);
            throw new MyException("没有找到可重新上传的{$statusText}任务");
        }

        // 验证分类关联并准备重新上传的数据
        $validDetails = [];
        $invalidDetails = [];

        foreach ($targetDetails as $detail) {
            if ($detail->category_id <= 0) {
                $invalidDetails[] = [
                    'id' => $detail->id,
                    'goods_name' => $detail->goods_name_ai,
                    'reason' => '没有关联N11分类'
                ];
                continue;
            }

            $n11Category = $this->productCategoryN11Model->find($detail->category_id);
            if (!$n11Category) {
                $invalidDetails[] = [
                    'id' => $detail->id,
                    'goods_name' => $detail->goods_name_ai,
                    'reason' => 'N11分类不存在'
                ];
                continue;
            }

            $validDetails[] = [
                'id' => $detail->id,
                'goods_name' => $detail->goods_name_ai,
                'thumb_url' => uploadFilePath($detail->thumb_url),
                'price' => $detail->price,
                'currentcy_goods' => $detail->currentcy_goods,
                'price_third' => $detail->price_third,
                'currentcy' => $detail->currentcy,
            ];
        }

        // 注意：这里不再重置任务状态，而是返回详情供前端处理
        return [
            'total_requested' => $targetDetails->count(),
            'valid_count' => count($validDetails),
            'invalid_count' => count($invalidDetails),
            'valid_details' => $validDetails,
            'invalid_details' => $invalidDetails,
            'message' => "找到 " . count($validDetails) . " 个可重新上传的任务，" . count($invalidDetails) . " 个任务因分类问题无法重新上传。请在前端执行批量上传。"
        ];
    }


    /**
     * 根据stockCode查找任务详情
     * @param int $userId 用户ID
     * @param string $stockCode 库存代码
     * @return array
     * @throws MyException
     */
    public function findByStockCode(int $userId, string $stockCode): array
    {
        if(empty($userId)){
            throw new MyException('用户ID不能为空');
        }
        if(empty($stockCode)){
            throw new MyException('stockCode不能为空');
        }

        $detail = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('stock_code', $stockCode)
            ->with(['task', 'store'])
            ->first();
            
        if (!$detail) {
            throw new MyException('未找到对应的任务详情记录');
        }

        return [
            'id' => $detail->id,
            'task_id' => $detail->task_id,
            'user_id' => $detail->user_id,
            'stock_code' => $detail->stock_code,
            'goods_name' => $detail->goods_name,
            'status' => $detail->status
        ];
    }

    /**
     * 获取商品图片列表
     * @param UserTaskDetailModel $detail 任务详情
     * @return array
     */
    private function getProductImages($detail): array
    {
        // 按照原逻辑：优先使用 goodsSku->skc_gallery，没有时才用 goods->goods_pic
        if ($detail->goodsSku && $detail->goodsSku->is_skc_gallery == 1) {
            $skc_gallery = $detail->goodsSku->skc_gallery;
        } else {
            $skc_gallery = $detail->goods ? $detail->goods->goods_pic : '';
        }
        // 解析JSON并去重过滤
        $decoded_skc_gallery = json_decode($skc_gallery, true);

        // 如果解码结果不是数组，则初始化为空数组
        if (!is_array($decoded_skc_gallery)) {
            $decoded_skc_gallery = [];
        }

        // 提取所有图片的URL
        $imageUrls = [];
        foreach ($decoded_skc_gallery as $item) {
            if (isset($item['url'])) {
                $imageUrls[] = $item['url'];
            }
        }

        // 对URL进行去重过滤
        $skc_gallery = array_values(array_filter(array_unique($imageUrls)));
        
        // 如果有 goodsSku->thumb_url，放到最前面
        if ($detail->goodsSku && $detail->goodsSku->thumb_url) {
            $skc_gallery = array_merge([$detail->goodsSku->thumb_url], $skc_gallery);
        }
        
        // 转换为带order的格式
        $ordered_skc_gallery = [];
        $order = 0;
        foreach ($skc_gallery as $item) {
            $ordered_skc_gallery[] = [
                'url' => uploadFilePath($item),
                'order' => $order++,
            ];
        }
        
        return $ordered_skc_gallery;
    }

    /**
     * 获取商品详情图片列表
     * @param UserTaskDetailModel $detail 任务详情
     * @return array
     */
    private function getProductImagesDetail($detail): array
    {
        $image_detail = $detail->goods ? $detail->goods->goods_detail : '';
        if(empty($image_detail)){
            $image_detail = [];
        }else{
            $image_detail = json_decode($image_detail, true);
            try{
                if(empty($image_detail)){
                    $image_detail = [];
                }else{
                    $image_detail = array_filter($image_detail, function($item) {
                        return is_string($item) && !empty(trim($item));
                    });
                    $image_detail = array_values(array_unique($image_detail));
                    $image_detail = array_map(function($item) {
                        return uploadFilePath($item);
                    }, $image_detail);
                }
            }catch(\Exception $e){
                $image_detail = [];
            }
        }
        
        return $image_detail;
    }

    /**
     * 格式化任务数据
     * @param UserTaskModel $task 任务模型
     * @return array 格式化后的任务数据
     */
    private function formatTaskData($task): array
    {
        return [
            'id' => $task->id,
            'user_id' => $task->user_id,
            'is_selected' => $task->is_selected,
            'selected_ids' => $task->selected_ids ? json_decode($task->selected_ids, true) : [],
            'time_range' => $task->time_range,
            'day_start' => $task->day_start,
            'day_end' => $task->day_end,
            'sort_order' => $task->sort_order,
            'execute_type' => $task->execute_type,
            'directory_id' => $task->directory_id,
            'user_account_id' => $task->user_account_id,
            'account_setting_type' => $task->account_setting_type,
            'price_rate' => $task->price_rate,
            'price_add' => $task->price_add,
            'price_subtract' => $task->price_subtract,
            'quantity' => $task->quantity,
            'vat_rate' => $task->vat_rate,
            'preparing_day' => $task->preparing_day,
            'store_name' => $task->store ? $task->store->account_name : '',
            'store_logo' => $task->store ? $task->store->account_logo : '',
            'directory_name' => $task->directory ? $task->directory->name : '',
            'task_count' => $task->task_count,
            'task_num' => $task->task_num,
            'latest_goods_id' => $task->latest_goods_id,
            'latest_time' => $task->latest_time,
            'task_over' => $task->task_over,
            'memo' => $task->memo,
            'created_at' => $task->created_at,
            'updated_at' => $task->updated_at,
        ];
    }

    /**
     * 格式化任务详情列表数据
     * @param UserTaskDetailModel $detail 任务详情模型
     * @return array 格式化后的数据
     */
    private function formatTaskDetailListData($detail): array
    {
        $statusMap = [
            0 => '等待上传',
            1 => '已成功上传',
            2 => '已上传待查询结果',
            3 => '上传失败',
            4 => '已存在',
            5 => '没有第三方商品分类',
            6 => '审核未通过',
        ];

        $statusTypeMap = [
            0 => 'warning',
            1 => 'success',
            2 => 'info',
            3 => 'danger',
            4 => 'info',
            5 => 'danger',
            6 => 'danger'
        ];


        $displayGoodsName = $detail->goods_name_ai;
        if(empty($displayGoodsName)){
            //兼容之前的逻辑
            $displayGoodsName = $detail->goods_name;
        }

        // 获取N11分类信息
        $n11Category = null;
        if ($detail->category_id > 0) {
            $n11Category = $this->productCategoryN11Model->find($detail->category_id);
        }

        return [
            'id' => $detail->id,
            'goods_name' => $displayGoodsName,
            'thumb_url' => $detail->thumb_url ? uploadFilePath($detail->thumb_url) : '',
            'spec_key_values' => $detail->spec_key_values,
            'spec_values' => $detail->spec_values,
            'price' => $detail->price,
            'price_third' => $detail->price_third,
            'price_list_third' => $detail->price_list_third,
            'currentcy' => $detail->currentcy,
            'currentcy_goods' => $detail->currentcy_goods,
            'quantity' => $detail->quantity,
            'vat_rate' => $detail->vat_rate,
            'preparing_day' => $detail->preparing_day,
            'category_id' => $detail->category_id,
            'is_cat_modified' => $detail->is_cat_modified,
            'n11_category' => $n11Category ? [
                'id' => $n11Category->id,
                'name' => $n11Category->name,
                'name_tl' => $n11Category->name_tl,
                'path_name' => str_replace(',', '->', $n11Category->path_name),
                'path_name_tl' => str_replace(',', '->', $n11Category->path_name_tl),
                'level' => $n11Category->level,
                'is_leaf' => $n11Category->is_leaf
            ] : null,
            'product_main_id' => $detail->product_main_id,
            'stock_code' => $detail->stock_code,
            'status' => $detail->status,
            'status_text' => $statusMap[$detail->status] ?? '未知状态',
            'status_type' => $statusTypeMap[$detail->status] ?? 'info',
            'third_task_id' => $detail->third_task_id,
            'third_type' => $detail->third_type,
            'third_status' => $detail->third_status,
            'third_result' => $detail->third_result,
            'memo' => $detail->memo,
            'store_name' => $detail->store ? $detail->store->account_name : '',
            'goods_info' => $detail->goods ? [
                'id' => $detail->goods->id,
                'goods_name' => $displayGoodsName,
                'source_url' => $detail->goods->source_url
            ] : null,
            'upload_params' => null,
            'created_at' => $detail->created_at,
            'updated_at' => $detail->updated_at,
        ];
    }
} 