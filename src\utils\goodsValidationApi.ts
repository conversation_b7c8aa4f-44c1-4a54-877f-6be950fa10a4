import config from '@/config';
import { ElMessageBox } from 'element-plus';
import { getLocalStorageData } from '@/utils/new/utils';

/**
 * 从Temu商品页面URL中提取商品ID
 * 支持两种URL格式：
 * 1. 路径格式：lik-açma-aleti-g-601100790372740.html?_oak_mp_inf=EISz1viq1ogBGhVm
 *    需要提取的ID：601100790372740（g-后面的数字部分）
 * 2. 查询参数格式：goods.html?_bg_fs=1&goods_id=601099591654793
 *    需要提取的ID：601099591654793（goods_id参数值）
 * @param url 商品页面URL
 * @returns 商品ID或null
 */
export function extractGoodsIdFromUrl(url: string): string | null {
  try {
    // 方式1：首先尝试从URL路径中匹配 g- 后面跟着数字的模式
    const pathMatch = url.match(/g-(\d+)/);
    if (pathMatch && pathMatch[1]) {
      return pathMatch[1];
    }

    // 方式2：如果路径匹配失败，尝试从查询参数中提取 goods_id
    try {
      const urlObj = new URL(url);
      const goodsId = urlObj.searchParams.get('goods_id');
      if (goodsId && /^\d+$/.test(goodsId)) {
        return goodsId;
      }
    } catch (urlError) {
      // URL解析失败，继续尝试正则匹配查询参数
      const queryMatch = url.match(/[?&]goods_id=(\d+)/);
      if (queryMatch && queryMatch[1]) {
        return queryMatch[1];
      }
    }

    return null;
  } catch (error) {
    console.error('提取商品ID失败:', error);
    return null;
  }
}

/**
 * 通过background页面发送商品ID检查请求
 * @param goodsId 商品ID
 * @returns Promise<{exists: boolean}>
 */
export async function sendCheckGoodsExistsRequest(goodsId: string): Promise<{exists: boolean}> {
  // 获取本地存储的 token
    const localData = await getLocalStorageData(['token']) as { token?: string };
    const token = localData.token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = 'Bearer ' + token;
    }
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: config.apiTemuCheckGoodsExistsUrl,
      method: 'post',
      pramas: { goods_id: goodsId },
      headers: headers as any,
      timeout: 30000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      if (response && response[0]) {
        const result = response[0];
        if (result.status >= 200 && result.status < 300) {
          const data = result.data;
          if (data.code === 1) {
            // 成功响应
            resolve({ exists: data.data.exists });
          } else {
            // 业务错误
            reject(new Error(data.msg || '检查商品ID失败'));
          }
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

/**
 * 检查商品ID是否存在
 * @param goodsId 商品ID
 * @returns Promise<{exists: boolean}>
 */
export async function checkGoodsExists(goodsId: string): Promise<{exists: boolean}> {
  if (!goodsId) {
    throw new Error('商品ID不能为空');
  }

  try {
    const result = await sendCheckGoodsExistsRequest(goodsId);
    return result;
  } catch (error) {
    console.error('检查商品ID存在性失败:', error);
    throw error;
  }
}

/**
 * 从当前页面URL检查商品ID是否存在
 * @param url 当前页面URL，如果不提供则使用window.location.href
 * @returns Promise<{exists: boolean, goodsId: string | null}>
 */
export async function checkCurrentPageGoodsExists(url?: string): Promise<{exists: boolean, goodsId: string | null}> {
  const currentUrl = url || window.location.href;
  const goodsId = extractGoodsIdFromUrl(currentUrl);

  if (!goodsId) {
    throw new Error('无法从当前页面URL中提取商品ID');
  }

  try {
    const result = await checkGoodsExists(goodsId);
    return {
      exists: result.exists,
      goodsId: goodsId
    };
  } catch (error) {
    console.error('检查当前页面商品ID存在性失败:', error);
    throw error;
  }
}

/**
 * 显示商品已存在的提示弹窗
 * @param goodsId 商品ID
 * @returns Promise<boolean> 用户是否点击了确定
 */
export function showGoodsExistsAlert(goodsId: string): Promise<boolean> {
  return new Promise(async (resolve) => {
    const message = `该商品ID ${goodsId} 已存在，请重新选择商品。`;

    try {
      // 使用 Element Plus 的美观弹窗组件
      await ElMessageBox.alert(message, '商品重复提示', {
        confirmButtonText: '确认',
        type: 'warning',
        center: true, // 居中显示
        customClass: 'goods-exists-alert', // 自定义样式类
        showClose: false, // 不显示关闭按钮，强制用户点击确认
        closeOnClickModal: false, // 点击遮罩层不关闭
        closeOnPressEscape: false, // 按ESC键不关闭
        beforeClose: (action, _instance, done) => {
          // 只有点击确认按钮才能关闭
          if (action === 'confirm') {
            done();
          }
        }
      });

      // 用户点击确认按钮后执行
      resolve(true);
    } catch (error) {
      // 如果弹窗出现异常，也返回true以确保流程继续
      console.warn('Element Plus 弹窗异常，使用默认处理:', error);
      resolve(true);
    }
  });
}

/**
 * 商品ID验证的主要入口函数
 * 在执行商品采集前调用此函数进行验证
 * @param url 可选的URL，如果不提供则使用当前页面URL
 * @returns Promise<boolean> 是否可以继续执行后续操作
 */
export async function validateGoodsBeforeCollection(url?: string): Promise<boolean> {
  try {
    console.log('开始验证商品ID...');

    const result = await checkCurrentPageGoodsExists(url);

    if (result.exists) {
      console.log(`商品ID ${result.goodsId} 已存在，显示提示弹窗`);
      await showGoodsExistsAlert(result.goodsId!);
      return false; // 商品已存在，不允许继续
    } else {
      console.log(`商品ID ${result.goodsId} 不存在，可以继续采集`);
      return true; // 商品不存在，可以继续
    }
  } catch (error) {
    console.error('商品ID验证失败:', error);
    // 验证失败时，为了安全起见，也不允许继续
    try {
      await ElMessageBox.alert('商品ID验证失败，请稍后重试。', '验证失败', {
        confirmButtonText: '确认',
        type: 'error',
        center: true
      });
    } catch (alertError) {
      // 如果Element Plus弹窗也失败，使用原生alert作为备选
      console.warn('Element Plus 弹窗异常，使用原生alert:', alertError);
      alert('商品ID验证失败，请稍后重试。');
    }
    return false;
  }
}

/**
 * 批量检查商品ID是否存在
 * @param goodsIds 商品ID数组
 * @returns Promise<Set<string>> 已存在的商品ID集合
 */
export async function batchValidateGoodsBeforeCollection(goodsIds: string[]): Promise<Set<string>> {
  if (!goodsIds || goodsIds.length === 0) {
    console.warn('商品ID数组为空');
    return new Set();
  }

  // 限制最大检查数量
  const maxBatchSize = 100;
  if (goodsIds.length > maxBatchSize) {
    console.warn(`商品ID数量超过最大限制(${maxBatchSize})，将截取前${maxBatchSize}个`);
    goodsIds = goodsIds.slice(0, maxBatchSize);
  }

  try {
    console.log(`开始批量验证${goodsIds.length}个商品ID...`);

    // 获取本地存储的 token
    const localData = await getLocalStorageData(['token']) as { token?: string };
    const token = localData.token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = 'Bearer ' + token;
    }

    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        funType: 'axios',
        url: config.apiTemuBatchCheckGoodsExistsUrl,
        method: 'post',
        pramas: { goods_ids: goodsIds },
        headers: headers as any,
        timeout: 30000
      }, (response: any) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }

        if (response && response[0]) {
          const result = response[0];
          if (result.status == 200) {
            const data = result.data;
            if (data.code === 1) {
              // 成功响应，处理返回的映射数据
              const existsMap = data.data.goods_map || {};
              console.log('existsMap::::',existsMap);
              const existingGoodsIds = new Set<string>();

              // 收集所有存在的商品ID
              Object.keys(existsMap).forEach(goodsId => {
                if (existsMap[goodsId] === true) {
                  existingGoodsIds.add(goodsId);
                }
              });

              console.log(`批量验证完成，发现${existingGoodsIds.size}个重复商品ID`);
              resolve(existingGoodsIds);
            } else {
              // 业务错误
              reject(new Error(data.msg || '批量检查商品ID失败'));
            }
          } else {
            reject(new Error(`请求失败: ${result.status}`));
          }
        } else {
          reject(new Error('请求未返回有效数据'));
        }
      });
    });

  } catch (error) {
    console.error('批量商品ID验证失败:', error);
    throw error;
  }
}

/**
 * 商品ID重复检查（仅显示提示，不阻止后续流程）
 * 用于在用户登录后立即检查商品是否已存在，如果存在则显示提示但继续后续流程
 * @param url 可选的URL，如果不提供则使用当前页面URL
 * @returns Promise<void> 无返回值，仅显示提示
 */
export async function checkAndShowGoodsDuplicateAlert(url?: string): Promise<void> {
  try {
    console.log('开始检查商品ID重复...');

    const result = await checkCurrentPageGoodsExists(url);

    if (result.exists) {
      console.log(`商品ID ${result.goodsId} 已存在，显示提示弹窗但继续后续流程`);
      await showGoodsExistsAlert(result.goodsId!);
      console.log('用户确认商品重复提示，继续后续流程');
    } else {
      console.log(`商品ID ${result.goodsId} 不存在，无需提示`);
    }
  } catch (error) {
    console.error('商品ID重复检查失败:', error);
    // 检查失败时不阻止后续流程，只记录错误
  }
}
