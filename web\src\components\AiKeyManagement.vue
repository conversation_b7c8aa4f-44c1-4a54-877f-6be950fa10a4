<template>
  <div class="ai-key-management">
    <div class="page-header">
      <h2>AI Key管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增AI Key
        </el-button>
        <el-button type="warning" @click="showBatchDialog" :disabled="selectedAiKeys.length === 0">
          <el-icon><Setting /></el-icon>
          批量设置
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="AI Key">
          <el-input v-model="searchForm.ai_key" placeholder="请输入AI Key" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="AI类型">
          <el-select v-model="searchForm.ai_type" placeholder="请选择AI类型" clearable style="width: 180px;">
            <el-option label="DeepSeek" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="aiKeyList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="AI Key" prop="ai_key" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="ai-key-cell">
              <span class="ai-key-text">{{ maskAiKey(row.ai_key) }}</span>
              <el-button
                type="text"
                size="small"
                @click="copyAiKey(row.ai_key)"
                class="copy-btn"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" min-width="200" show-overflow-tooltip />
        <el-table-column label="AI类型" prop="ai_type_name" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.ai_type_name }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort_order" width="80" />
        <el-table-column label="状态" prop="status" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ row.status_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="success" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑AI Key对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="AI Key" prop="ai_key">
          <el-input
            v-model="form.ai_key"
            type="textarea"
            :rows="3"
            placeholder="请输入AI Key"
            show-word-limit
            maxlength="500"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入AI Key描述（可选）"
            show-word-limit
            maxlength="500"
          />
        </el-form-item>
        <el-form-item label="AI类型" prop="ai_type">
          <el-select v-model="form.ai_type" placeholder="请选择AI类型" style="width: 100%">
            <el-option label="DeepSeek" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="form.sort_order"
            :min="0"
            :max="999999"
            placeholder="按数字降序排列"
            style="width: 200px"
          />
          <div class="form-item-tip">数字越大越靠前，按降序排列</div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量设置对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量设置"
      width="500px"
    >
      <el-form
        ref="batchFormRef"
        :model="batchForm"
        :rules="batchFormRules"
        label-width="100px"
      >
        <el-form-item label="状态">
          <el-select v-model="batchForm.status" placeholder="请选择状态" clearable style="width: 100%">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <div class="batch-info">
          <p>将对以下 {{ selectedAiKeys.length }} 个AI Key进行批量设置：</p>
          <ul class="selected-keys">
            <li v-for="aiKey in selectedAiKeys" :key="aiKey.id">
              {{ aiKey.description || maskAiKey(aiKey.ai_key) }}
            </li>
          </ul>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus, Setting, Search, Refresh, DocumentCopy } from '@element-plus/icons-vue'
import {
  getAiKeyList,
  createAiKey,
  updateAiKey,
  deleteAiKey,
  batchUpdateAiKey,
  AI_TYPE_NAMES,
  type AiKey,
  type AiKeyListParams,
  type AiKeyFormData,
  type AiKeyBatchUpdateData
} from '../utils/aiKeyApi'

// 接口定义
interface SearchForm {
  ai_key: string
  status: number | null | undefined
  ai_type: number | null | undefined
  start_date?: string
  end_date?: string
}

interface BatchForm {
  status: number | null | undefined
}

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const aiKeyList = ref<AiKey[]>([])
const selectedAiKeys = ref<AiKey[]>([])

// 搜索表单
const searchForm = reactive<SearchForm>({
  ai_key: '',
  status: null,
  ai_type: null
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 对话框状态
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const isEdit = ref(false)

// 表单数据
const form = reactive<AiKeyFormData>({
  ai_key: '',
  description: '',
  sort_order: 0,
  status: 1,
  ai_type: 1
})

const batchForm = reactive<BatchForm>({
  status: undefined
})

const formRef = ref()
const batchFormRef = ref()

// 表单验证规则
const formRules = {
  ai_key: [
    { required: true, message: '请输入AI Key', trigger: 'blur' },
    { min: 1, max: 500, message: 'AI Key长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  ai_type: [
    { required: true, message: '请选择AI类型', trigger: 'change' }
  ]
}

const batchFormRules = {}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑AI Key' : '新增AI Key')

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.start_date = dates[0]
    searchForm.end_date = dates[1]
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 遮罩AI Key显示
const maskAiKey = (aiKey: string) => {
  if (!aiKey) return ''
  if (aiKey.length <= 10) return aiKey
  return `${aiKey.substring(0, 6)}...${aiKey.substring(aiKey.length - 4)}`
}

// 复制AI Key
const copyAiKey = async (aiKey: string) => {
  try {
    await navigator.clipboard.writeText(aiKey)
    ElMessage.success('AI Key已复制到剪贴板')
  } catch (error) {
    // 降级处理
    const textarea = document.createElement('textarea')
    textarea.value = aiKey
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    ElMessage.success('AI Key已复制到剪贴板')
  }
}

// 加载AI Key列表
const loadAiKeyList = async () => {
  loading.value = true
  try {
    const params: AiKeyListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ai_key: searchForm.ai_key || undefined,
      status: (searchForm.status === null || searchForm.status === undefined) ? undefined : searchForm.status,
      ai_type: (searchForm.ai_type === null || searchForm.ai_type === undefined) ? undefined : searchForm.ai_type,
      start_date: searchForm.start_date,
      end_date: searchForm.end_date
    }

    const response = await getAiKeyList(params)

    // 处理数据，添加状态名称和AI类型名称
    aiKeyList.value = response.list.map(item => ({
      ...item,
      status_name: item.status === 1 ? '启用' : '禁用',
      ai_type_name: AI_TYPE_NAMES[item.ai_type as keyof typeof AI_TYPE_NAMES] || '未知'
    }))

    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.error('获取AI Key列表失败:', error)
    ElMessage.error('获取AI Key列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadAiKeyList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    ai_key: '',
    status: null,
    ai_type: null,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.currentPage = 1
  loadAiKeyList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadAiKeyList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadAiKeyList()
}

// 选择变化
const handleSelectionChange = (selection: AiKey[]) => {
  selectedAiKeys.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  isEdit.value = false
  Object.assign(form, {
    ai_key: '',
    description: '',
    sort_order: 0,
    status: 1,
    ai_type: 1
  })
  dialogVisible.value = true
}

// 显示批量设置对话框
const showBatchDialog = () => {
  batchForm.status = undefined
  batchDialogVisible.value = true
}

// 编辑
const handleEdit = (row: AiKey) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    ai_key: row.ai_key,
    description: row.description,
    sort_order: row.sort_order,
    status: row.status,
    ai_type: row.ai_type
  })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: AiKey) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除AI Key"${row.description || maskAiKey(row.ai_key)}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteAiKey(row.id)
    ElMessage.success('AI Key删除成功')
    loadAiKeyList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除AI Key失败:', error)
      ElMessage.error('删除AI Key失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 先进行表单验证，如果验证失败直接返回，不显示错误信息
    const isValid = await formRef.value.validate().catch(() => false)
    if (!isValid) {
      return
    }

    submitting.value = true

    if (isEdit.value) {
      await updateAiKey(form)
      ElMessage.success('AI Key更新成功')
    } else {
      await createAiKey(form)
      ElMessage.success('AI Key创建成功')
    }

    dialogVisible.value = false
    loadAiKeyList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 批量设置提交
const handleBatchSubmit = async () => {
  try {
    await batchFormRef.value.validate()

    // 检查是否选择了至少一个设置项
    if (batchForm.status === undefined) {
      ElMessage.warning('请选择状态')
      return
    }

    batchSubmitting.value = true

    const ids = selectedAiKeys.value.map(aiKey => aiKey.id)
    const updateData: AiKeyBatchUpdateData = {
      ids,
      ...(batchForm.status !== undefined && { status: batchForm.status })
    }

    await batchUpdateAiKey(updateData)

    ElNotification({
      title: '批量设置成功',
      message: `已成功设置 ${selectedAiKeys.value.length} 个AI Key。`,
      type: 'success'
    })

    batchDialogVisible.value = false
    selectedAiKeys.value = []
    loadAiKeyList()
  } catch (error) {
    console.error('批量设置失败:', error)
    ElMessage.error('批量设置失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAiKeyList()
})
</script>

<style scoped>
.ai-key-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ai-key-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-key-text {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.copy-btn {
  padding: 4px;
  min-height: auto;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.batch-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.batch-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: bold;
}

.selected-keys {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.selected-keys li {
  color: #409eff;
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单提示样式 */
.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>
