<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;

class CleanupDuplicateTaskDetails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:duplicate-task-details';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理user_task_detail表中的重复数据，保留每组重复记录中创建时间最早的记录';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始清理user_task_detail表中的重复数据...');
        
        try {
            // 1. 查询重复记录
            $duplicates = $this->getDuplicateRecords();
            
            if (empty($duplicates)) {
                $this->info('未发现重复记录，无需清理。');
                return Command::SUCCESS;
            }

            // 2. 显示重复记录统计
            $totalDuplicates = collect($duplicates)->sum('duplicate_count') - count($duplicates);
            $this->warn("发现重复记录组数: " . count($duplicates));
            $this->warn("预计删除记录数: " . $totalDuplicates);

            // 3. 显示详细重复记录信息
            $this->displayDuplicateDetails($duplicates);

            // 4. 要求用户确认
            if (!$this->confirm('是否继续执行清理操作？(y/n)')) {
                $this->info('操作已取消。');
                return Command::SUCCESS;
            }

            // 5. 执行清理操作
            $this->cleanupDuplicates($duplicates);

            $this->info('清理完成！');
            return Command::SUCCESS;

        } catch (QueryException $e) {
            $this->error('数据库查询错误: ' . $e->getMessage());
            Log::error('CleanupDuplicateTaskDetails 数据库错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        } catch (\Exception $e) {
            $this->error('执行错误: ' . $e->getMessage());
            Log::error('CleanupDuplicateTaskDetails 执行错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 获取重复记录
     *
     * @return array
     */
    private function getDuplicateRecords(): array
    {
        return DB::table('user_task_detail')
            ->select('task_id', 'user_goods_sku_id', 
                DB::raw('COUNT(*) as duplicate_count'),
                DB::raw('MIN(created_at) as earliest_created_at'),
                DB::raw('GROUP_CONCAT(id) as all_ids')
            )
            ->groupBy('task_id', 'user_goods_sku_id')
            ->having('duplicate_count', '>', 1)
            ->get()
            ->toArray();
    }

    /**
     * 显示重复记录详细信息
     *
     * @param array $duplicates
     */
    private function displayDuplicateDetails(array $duplicates): void
    {
        $this->table(
            ['Task ID', 'SKU ID', '重复次数', '最早创建时间', '待删除记录数'],
            collect($duplicates)->map(function ($duplicate) {
                return [
                    $duplicate->task_id,
                    $duplicate->user_goods_sku_id,
                    $duplicate->duplicate_count,
                    $duplicate->earliest_created_at,
                    $duplicate->duplicate_count - 1
                ];
            })->toArray()
        );
    }

    /**
     * 清理重复记录
     *
     * @param array $duplicates
     */
    private function cleanupDuplicates(array $duplicates): void
    {
        $totalDeleted = 0;
        $progressBar = $this->output->createProgressBar(count($duplicates));
        $progressBar->start();

        foreach ($duplicates as $duplicate) {
            // 获取该组的所有记录ID
            $ids = collect(explode(',', $duplicate->all_ids))->map(fn($id) => (int)$id);
            
            // 找到最早创建的记录ID（保留）
            $keepId = DB::table('user_task_detail')
                ->where('task_id', $duplicate->task_id)
                ->where('user_goods_sku_id', $duplicate->user_goods_sku_id)
                ->where('created_at', $duplicate->earliest_created_at)
                ->value('id');

            // 获取要删除的ID列表（排除最早创建的记录）
            $deleteIds = $ids->filter(fn($id) => $id !== $keepId);

            if ($deleteIds->isNotEmpty()) {
                // 批量删除重复记录
                $deleted = DB::table('user_task_detail')
                    ->whereIn('id', $deleteIds)
                    ->delete();
                
                $totalDeleted += $deleted;
                Log::info('CleanupDuplicateTaskDetails 删除重复记录', [
                    'task_id' => $duplicate->task_id,
                    'user_goods_sku_id' => $duplicate->user_goods_sku_id,
                    'deleted_count' => $deleted,
                    'kept_id' => $keepId
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        
        $this->info("清理完成！共删除 {$totalDeleted} 条重复记录。");
    }
}