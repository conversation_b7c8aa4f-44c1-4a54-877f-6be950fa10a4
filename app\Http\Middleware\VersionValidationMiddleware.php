<?php
declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\MyException;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class VersionValidationMiddleware
{
    /**
     * 处理版本验证
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查版本验证是否启用
        if (!config('version.validation_enabled', true)) {
            return $next($request);
        }

        // 从请求中提取版本号（此时请求参数已经被DecryptRequest中间件解密）
        $version = $this->extractVersionFromRequest($request);
        // 如果版本号缺失
        if (empty($version)) {
            /* $message = config('version.messages.version_missing', '缺少版本号信息，请联系客服获取最新版本');
            return $this->createErrorResponse(2004, $message, null); */
            throw new MyException('您需要升级到最新版本才能使用,请联系客服获取最新版本');

        }

        // 验证版本号格式
        if (!$this->validateVersionFormat($version)) {
            $message = config('version.messages.version_invalid_format', '您需要升级到最新版本才能使用,请联系客服获取最新版本');

            return $this->createErrorResponse(2003, $message, $version);
        }

        // 获取版本配置
        $minVersion = config('version.min_supported_version');
        $maxVersion = config('version.max_supported_version');

        // 验证版本范围
        if (!$this->compareVersions($version, $minVersion, $maxVersion)) {
            $errorCode = $this->getVersionErrorCode($version, $minVersion, $maxVersion);
            $message = $this->getVersionErrorMessage($errorCode);
            
            return $this->createErrorResponse(
                $errorCode,
                $message,
                $version,
                [
                    'min_version' => $minVersion,
                    'max_version' => $maxVersion,
                    'latest_version' => config('version.current_latest_version')
                ]
            );
        }

        return $next($request);
    }

    /**
     * 从请求中提取版本号
     *
     * @param Request $request
     * @return string|null
     */
    private function extractVersionFromRequest(Request $request): ?string
    {
        // 优先从请求参数中获取版本号
        $version = $request->input('app_version');
        
        if (empty($version)) {
            // 如果请求参数中没有，尝试从查询参数中获取
            $version = $request->query('app_version');
        }

        return $version;
    }

    /**
     * 验证版本号格式
     *
     * @param string $version
     * @return bool
     */
    private function validateVersionFormat(string $version): bool
    {
        // 验证版本号格式：x.y.z，其中x、y、z都是数字
        return preg_match('/^\d+\.\d+\.\d+$/', $version) === 1;
    }

    /**
     * 比较版本号是否在允许范围内
     *
     * @param string $version
     * @param string $minVersion
     * @param string $maxVersion
     * @return bool
     */
    private function compareVersions(string $version, string $minVersion, string $maxVersion): bool
    {
        return version_compare($version, $minVersion, '>=') && 
               version_compare($version, $maxVersion, '<=');
    }

    /**
     * 获取版本错误码
     *
     * @param string $version
     * @param string $minVersion
     * @param string $maxVersion
     * @return int
     */
    private function getVersionErrorCode(string $version, string $minVersion, string $maxVersion): int
    {
        if (version_compare($version, $minVersion, '<')) {
            return 2001; // 版本过低
        }
        
        if (version_compare($version, $maxVersion, '>')) {
            return 2002; // 版本过高
        }
        
        return 2001; // 默认返回版本过低
    }

    /**
     * 获取版本错误消息
     *
     * @param int $errorCode
     * @return string
     */
    private function getVersionErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case 2001:
                return config('version.messages.version_too_low', '您需要升级到最新版本才能使用,请联系客服升级最新版本');

            case 2002:
                return config('version.messages.version_too_high', '您需要升级到最新版本才能使用,请联系客服升级最新版本');
            default:
                return config('version.messages.version_too_low', '您需要升级到最新版本才能使用,请联系客服升级最新版本');
        }
    }

    /**
     * 创建错误响应
     *
     * @param int $code
     * @param string $message
     * @param string|null $currentVersion
     * @param array $additionalData
     * @return JsonResponse
     */
    private function createErrorResponse(int $code, string $message, ?string $currentVersion, array $additionalData = []): JsonResponse
    {
        $data = array_merge([
            'current_version' => $currentVersion
        ], $additionalData);

        return response()->json([
            'status' => 400,
            'code' => $code,
            'msg' => $message,
            'data' => $data
        ], 400);
    }
}