#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QMessageBox, QApplication, QDesktopWidget)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from database.config_dao import ConfigDAO
from api.goods_service import GoodsService
from ui.worker_threads import GoodsDataWorker
from ui.components.config_panel import ConfigPanel
from ui.components.progress_panel import ProgressPanel
from ui.components.log_panel import LogPanel
from ui.components.status_bar import StatusBarComponent
from utils.logger import log_message, set_gui_log_callback, set_status_bar_log_callback
from utils.helpers import get_dpi_scale_factor
from config.settings import AppSettings


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 初始化数据库和服务
        self.db_manager = DatabaseManager()
        self.config_dao = ConfigDAO(self.db_manager)
        self.goods_service = GoodsService()

        # 工作线程
        self.goods_worker = None

        # UI组件
        self.config_panel = None
        self.progress_panel = None
        self.log_panel = None
        self.status_bar_component = None

        # 窗口高度管理 - 支持DPI感知
        self.base_initial_height = 280  # 基础初始高度（100% DPI时）
        self.base_expanded_height = 415  # 基础展开高度（100% DPI时）
        self.use_dynamic_height = True  # 是否使用动态高度调整
        
        # 计算DPI感知的实际高度
        self.dpi_scale_factor = get_dpi_scale_factor(verbose=True)
        self.initial_height = int(self.base_initial_height * self.dpi_scale_factor)
        self.expanded_height = int(self.base_expanded_height * self.dpi_scale_factor)

        # 只有在GUI模式下才设置GUI日志回调
        if AppSettings.get_default_log_output() == AppSettings.LOG_OUTPUT_GUI:
            set_gui_log_callback(self._gui_log_callback)

        self.init_ui()
        self.setup_connections()
        self.load_configs()

        # 在启动时显示数据库路径信息
        self.log_startup_info()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("跨境蜂助手 V1.0.0")

        # 设置窗口图标 - 完全隐藏标题栏图标
        self.set_window_icon_hidden()

        # 设置初始窗口大小（应用DPI缩放）
        window_width = int(800 * self.dpi_scale_factor)
        min_width = int(800 * self.dpi_scale_factor)
        min_height = int(250 * self.dpi_scale_factor)
        
        self.resize(window_width, self.initial_height)
        self.setMinimumWidth(min_width)
        self.setMinimumHeight(min_height)

        # 窗口居中显示
        self.center_window()

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(5)

        # 创建配置管理面板
        self.config_panel = ConfigPanel(self.config_dao)
        main_layout.addWidget(self.config_panel)

        # 创建进度显示面板
        self.progress_panel = ProgressPanel()
        main_layout.addWidget(self.progress_panel)

        # 根据配置决定初始显示状态
        if self.use_dynamic_height:
            self.progress_panel.setVisible(False)  # 动态方案：初始隐藏
        else:
            self.progress_panel.setVisible(True)   # 备选方案：始终显示

        # 根据配置决定是否创建日志显示区域
        if AppSettings.get_default_log_output() == AppSettings.LOG_OUTPUT_GUI:
            # 创建日志显示区域
            self.log_panel = LogPanel()
            main_layout.addWidget(self.log_panel)

            # 设置布局比例
            main_layout.setStretch(0, 0)  # 配置区域固定高度
            main_layout.setStretch(1, 0)  # 进度区域固定高度
            main_layout.setStretch(2, 1)  # 日志区域可伸缩

            # 有日志区域时使用更大的高度（应用DPI缩放）
            self.initial_height = int(450 * self.dpi_scale_factor)
            self.expanded_height = int(565 * self.dpi_scale_factor)
            window_width = int(800 * self.dpi_scale_factor)
            self.resize(window_width, self.initial_height)
        else:
            # 不创建日志相关组件时，添加一个弹性空间使配置区域靠上
            main_layout.addStretch(1)
            # 设置布局比例
            main_layout.setStretch(0, 0)  # 配置区域固定高度
            main_layout.setStretch(1, 0)  # 进度区域固定高度
            main_layout.setStretch(2, 1)  # 弹性空间可伸缩
            # 调整窗口大小到初始高度（应用DPI缩放）
            window_width = int(800 * self.dpi_scale_factor)
            self.resize(window_width, self.initial_height)

        # 创建状态栏
        self.status_bar_component = StatusBarComponent()
        self.setStatusBar(self.status_bar_component)

    def set_window_icon_hidden(self):
        """完全隐藏标题栏图标"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap
            # 方法3: 创建1x1像素的透明图标
            pixmap = QPixmap(1, 1)
            pixmap.fill(Qt.transparent)
            self.setWindowIcon(QIcon(pixmap))
            
            print("✓ 标题栏图标已完全隐藏")

        except Exception as e:
            print(f"⚠️  隐藏窗口图标失败: {e}")



    def center_window(self):
        """将窗口居中显示到主显示器"""
        try:
            # 获取应用程序实例
            app = QApplication.instance()
            if not app:
                return

            # 获取主显示器的几何信息
            desktop = app.desktop()
            primary_screen = desktop.primaryScreen()
            screen_geometry = desktop.screenGeometry(primary_screen)

            # 获取窗口大小
            window_geometry = self.frameGeometry()

            # 计算居中位置
            center_x = screen_geometry.center().x() - window_geometry.width() // 2
            center_y = screen_geometry.center().y() - window_geometry.height() // 2

            # 确保窗口不会超出屏幕边界
            center_x = max(screen_geometry.left(), min(center_x, screen_geometry.right() - window_geometry.width()))
            center_y = max(screen_geometry.top(), min(center_y, screen_geometry.bottom() - window_geometry.height()))

            # 移动窗口到居中位置
            self.move(center_x, center_y)

        except Exception as e:
            # 如果居中失败，使用默认位置
            self.move(100, 100)

    def setup_connections(self):
        """设置信号连接"""
        # 只有在GUI模式下才设置GUI日志回调
        if AppSettings.get_default_log_output() == AppSettings.LOG_OUTPUT_GUI and self.log_panel:
            set_gui_log_callback(self.log_panel.append_log)

        # 总是设置状态栏日志回调，即使在静默模式下
        set_status_bar_log_callback(self.status_bar_component.update_status)

        # 连接配置面板信号
        self.config_panel.config_changed.connect(self.on_config_changed)
        self.config_panel.validation_error_occurred.connect(self._handle_worker_error)  # 连接验证错误信号

        # 连接进度面板信号
        self.progress_panel.stop_requested.connect(self.stop_processing)

        # 重写配置面板的处理商品数据方法
        self.config_panel.process_btn.clicked.disconnect()
        self.config_panel.process_btn.clicked.connect(self.process_goods_data)

    def load_configs(self):
        """加载配置"""
        self.config_panel.load_configs()

    def on_config_changed(self, appid: str, appsecret: str):
        """配置改变事件"""
        # 这里可以添加配置改变后的处理逻辑
        pass

    def _gui_log_callback(self, message: str, level: str):
        """GUI日志回调函数（兼容性保留）"""
        if self.log_panel:
            self.log_panel.append_log(message, level)

    def _handle_worker_error(self, error_message: str):
        """处理worker线程的错误信息"""
        # 直接调用状态栏的错误显示方法，确保以ERROR级别显示
        if self.status_bar_component:
            self.status_bar_component.update_status(error_message, "ERROR")

    def _handle_worker_info(self, info_message: str):
        """处理worker线程的信息提示"""
        # 调用状态栏的信息提示显示方法
        if self.status_bar_component:
            self.status_bar_component.show_info_message(info_message)

    def process_goods_data(self):
        """处理商品数据"""
        current_appid, current_appsecret = self.config_panel.get_current_config()

        if not current_appid or not current_appsecret:
            QMessageBox.warning(self, "警告", "请先验证配置")
            return

        log_message("开始处理商品数据...")

        # 禁用所有控件
        self.config_panel.set_controls_enabled(False)

        # 显示进度区域并调整窗口高度
        self.show_progress_area()

        # 创建并启动商品数据处理工作线程
        self.goods_worker = GoodsDataWorker(current_appid, current_appsecret)
        self.goods_worker.progress_updated.connect(log_message)
        self.goods_worker.error_occurred.connect(self._handle_worker_error)  # 连接错误信号
        self.goods_worker.info_message_occurred.connect(self._handle_worker_info)  # 连接信息提示信号
        self.goods_worker.goods_progress_updated.connect(self.progress_panel.update_goods_progress)
        self.goods_worker.file_progress_updated.connect(self.progress_panel.update_file_progress)
        self.goods_worker.processing_completed.connect(self.on_processing_completed)
        self.goods_worker.progress_reset_requested.connect(self.progress_panel.reset_progress)  # 连接进度重置信号
        self.goods_worker.progress_activate_requested.connect(self.progress_panel.activate_progress)  # 连接进度激活信号
        self.goods_worker.start()

    def stop_processing(self):
        """停止处理"""
        if self.goods_worker and self.goods_worker.isRunning():
            self.goods_worker.stop()
            self.goods_worker.wait(5000)  # 等待最多5秒

        # 执行清理工作，但不显示任何消息框
        self._cleanup_after_processing()

        # 只显示停止成功的提示
        QMessageBox.information(self, "提示", "停止操作成功")

    def _cleanup_after_processing(self):
        """处理完成后的清理工作"""
        # 隐藏进度区域并调整窗口高度
        self.hide_progress_area()

        # 重置进度显示
        self.progress_panel.reset_progress()

        # 启用所有控件
        self.config_panel.set_controls_enabled(True)

        # 清理工作线程
        self.goods_worker = None

    def on_processing_completed(self, success: bool, message: str, show_message_box: bool = True):
        """处理完成"""
        log_message(f"处理完成: {message}")

        # 执行清理工作
        self._cleanup_after_processing()

        # 只有在需要显示消息框时才显示
        if show_message_box:
            if success:
                QMessageBox.information(self, "成功", message)
            else:
                QMessageBox.warning(self, "提示", message)

    def animate_window_height(self, target_height):
        """动画调整窗口高度"""
        current_height = self.height()
        if current_height == target_height:
            return

        # 创建高度动画
        self.height_animation = QPropertyAnimation(self, b"size")
        self.height_animation.setDuration(300)  # 300ms动画时间
        self.height_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 设置起始和结束尺寸
        current_size = self.size()
        target_size = current_size
        target_size.setHeight(target_height)

        self.height_animation.setStartValue(current_size)
        self.height_animation.setEndValue(target_size)

        # 启动动画
        self.height_animation.start()

    def show_progress_area(self):
        """显示进度区域并调整窗口高度"""
        if self.use_dynamic_height:
            # 动态高度调整方案
            self.progress_panel.setVisible(True)
            self.progress_panel.set_active_state()
            self.animate_window_height(self.expanded_height)
        else:
            # 备选方案：始终显示进度区域，切换内容
            self.progress_panel.setVisible(True)
            self.progress_panel.set_active_state()

    def hide_progress_area(self):
        """隐藏进度区域并调整窗口高度"""
        if self.use_dynamic_height:
            # 动态高度调整方案
            self.progress_panel.setVisible(False)
            self.animate_window_height(self.initial_height)
        else:
            # 备选方案：保持显示进度区域，切换为等待状态
            self.progress_panel.set_waiting_state()

    def log_startup_info(self):
        """记录启动信息"""
        import os
        import sys
        from utils.helpers import get_app_directory, get_database_directory

        log_message("=== 跨境蜂助手启动 ===", "INFO")

        # 显示应用程序目录信息
        app_dir = get_app_directory()
        log_message(f"应用程序目录: {app_dir}")

        # 显示数据库目录信息
        db_dir = get_database_directory()
        log_message(f"数据库目录: {db_dir}")

        # 显示数据库文件路径
        db_path = self.db_manager.db_path
        log_message(f"数据库文件路径: {db_path}")

        # 检查数据库文件是否存在
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            log_message(f"数据库文件已存在，大小: {file_size} 字节", "SUCCESS")
        else:
            log_message("数据库文件不存在，首次运行将自动创建", "INFO")

        # 检查是否为打包后的环境
        if getattr(sys, 'frozen', False):
            log_message("运行环境: 打包后的EXE文件", "INFO")
            log_message(f"EXE文件路径: {sys.executable}")
        else:
            log_message("运行环境: 开发环境", "INFO")

        log_message("=== 跨境蜂助手已启动 ===", "INFO")