<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Http\Requests\Front\UserRegRequest;
use App\Service\Order\OrderService;
use App\Service\User\UserPriceRecordService;
use App\Service\User\UserService;
use App\Utils\Tools;
use Illuminate\Support\Str;

class UserController extends Controller
{
    protected UserService $userService;
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
        parent::__construct();
    }

    public function rewardGromoreCallback(){
        return $this->userService->rewardGromoreCallback();
    }

    public function rewardGdtCallback(){
        return $this->userService->rewardGdtCallback();
    }

    public function rewardKsCallback(){
        return $this->userService->rewardKsCallback();
    }

    public function rewardGromore(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->rewardGromore($user));
    }

    public function rewardGdt(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->rewardGdt($user));
    }

    public function rewardKs(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->rewardKs($user));
    }
    
    public function loginSms(){
        return $this->apiSuccess($this->userService->loginSms());
    }

    public function register(){
        return $this->apiSuccess($this->userService->register());
    }

    public function withdrawInfo(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->withdrawInfo($user));
    }

    public function getUserInfo(){
        $user = request()->attributes->get('user');
        
        return $this->apiSuccess($this->userService->getUserInfo($user));
    }

    public function getStoreInfo(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->getStoreInfo($user));
    }

    public function logout(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->logout($user));
    }


    public function getUser(){
        $user = request()->attributes->get('user');
        if(is_array($user)){
            if(isset($user['money'])){
                $user['money'] = format_money($user['money'], 2);
            }
        }

        $user['site_name'] = config('ad.system_name');
        
        return $this->apiSuccess($user);
    }


    public function canAd(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->canAd($user),200,'成功');
    }

    public function packetDescribe(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->packetDescribe($user));
    }

    public function collectPacketOne(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->collectPacketOne($user));
    }

    public function withdrawList(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->withdrawList($user['id']));
    }

    public function withdrawAdd(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->withdrawAdd($user));
    }

    public function moneyList(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->moneyList($user['id']));
    }

    public function adReward(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->userService->adReward($user));
    }

    public function adRewardInfoByUser(){
        $data = request()->all();
        return $this->apiSuccess($this->userService->adRewardInfoByUser($data));
    }

    public function adRewardAdd(){
        $user = request()->attributes->get('user');
        $user_id = $user['id'];
        $pid = $user['pid'];
        $weixin_image = $user['weixin_image'] ?? '';
        $data = request()->all();
        $data['user_id'] = $user_id;
        $data['pid'] = $pid;
        $data['weixin_image'] = $weixin_image;
        return $this->apiSuccess($this->userService->adRewardAdd($data));
    }

    public function adRewardList(){
        $user = request()->attributes->get('user');
        $data = request()->all();
        $data['user_id'] = $user['id'];
        $page = $data['page'] ?? 1;
        $page = $page < 1 ? 1 : $page;
        $page_size = $data['page_size'] ?? 10;
        $page_size = $page_size < 1 ? 1 : $page_size;
        return $this->apiSuccess($this->userService->adRewardList($data,$page,$page_size));
    }

    /**
     * 修改用户密码
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(){
        $user = request()->attributes->get('user');
        $data = request()->all();
        
        // 验证参数
        if (empty($data['oldPassword'])) {
            return $this->apiError('原密码不能为空');
        }
        if (empty($data['newPassword'])) {
            return $this->apiError('新密码不能为空');
        }
        if (strlen($data['newPassword']) < 6) {
            return $this->apiError('新密码长度不能少于6位');
        }

        try {
            $result = $this->userService->changePassword($user, $data['oldPassword'], $data['newPassword']);
            return $this->apiSuccess($result, 200, '密码修改成功');
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }






}

