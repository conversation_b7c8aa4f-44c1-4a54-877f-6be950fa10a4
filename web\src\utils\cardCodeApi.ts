/**
 * 卡密管理API接口
 * 参考aiKeyApi.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 卡密接口定义
export interface CardCode {
  id: number
  card_code: string
  card_name: string
  card_type: number
  card_type_text: string
  price: number
  points: number
  vip_days: number
  vip_days_unit: string
  vip_level: number
  status: number
  status_text: string
  used_at: string | null
  used_by: number | null
  used_by_phone: string
  is_copied: number
  copied_at: string | null
  copied_by: number | null
  copied_by_phone: string
  batch_no: string
  description: string
  valid_until: string | null
  admin_phone: string
  is_valid: boolean
  can_delete: boolean
  created_at: string
  updated_at: string
}

// 卡密列表参数
export interface CardCodeListParams {
  page?: number
  pageSize?: number
  card_type?: number
  status?: number
  is_copied?: number
  copied_by?: number
  admin_id?: number
  card_code?: string
  card_name?: string
  batch_no?: string
  start_date?: string
  end_date?: string
  min_price?: number
  max_price?: number
  min_points?: number
  max_points?: number
  vip_days_unit?: string // 卡密单位：year-年，month-月，day-天
  time_type?: string // 时间类型：created_at-创建时间，used_at-使用时间，copied_at-复制时间
  date_range?: string // 时间范围：all-全部，today-今天，yesterday-昨天，thisWeek-本周，thisMonth-本月，lastMonth-上月，firstHalf-上半年，secondHalf-下半年，thisYear-今年，lastYear-去年，custom-自定义
}

// 卡密表单数据
export interface CardCodeFormData {
  id?: number
  card_name: string
  card_type: number
  price: number
  points: number
  vip_days?: number
  vip_days_unit?: string
  vip_level?: number
  description?: string
  valid_until?: string
  prefix?: string
}

// 批量创建参数
export interface CardCodeBatchCreateData {
  quantity: number
  card_name: string
  card_type: number
  price: number
  points: number
  vip_days?: number
  vip_days_unit?: string
  vip_level?: number
  description?: string
  valid_until?: string
  prefix?: string
}

// API响应类型
export interface CardCodeListResponse {
  list: CardCode[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

export interface CardCodeResponse {
  id?: number
  message: string
}

export interface CardCodeBatchCreateResponse {
  batch_no: string
  success_count: number
  failed_count: number
  total_requested: number
  created_cards: any[]
}

export interface CardCodeBatchDeleteResponse {
  success_count: number
  failed_count: number
  total_requested: number
  failed_reasons: string[]
}

/**
 * 获取卡密列表（分页）
 */
export const getCardCodeList = async (params: CardCodeListParams): Promise<CardCodeListResponse> => {
  const url = await getApiUrl('apiCardCodeListUrl');
  console.log('获取卡密列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getCardCodeList',
    url,
    method: 'get',
    params,
    auth: true
  });
}

/**
 * 创建单个卡密
 */
export const createCardCode = async (data: CardCodeFormData): Promise<CardCodeResponse> => {
  const url = await getApiUrl('apiCardCodeCreateUrl');
  console.log('创建卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'createCardCode',
    url,
    method: 'post',
    data,
    auth: true
  });
}

/**
 * 批量生成卡密
 */
export const batchCreateCardCodes = async (data: CardCodeBatchCreateData): Promise<CardCodeBatchCreateResponse> => {
  const url = await getApiUrl('apiCardCodeBatchCreateUrl');
  console.log('批量生成卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'batchCreateCardCodes',
    url,
    method: 'post',
    data,
    auth: true
  });
}

/**
 * 更新卡密
 */
export const updateCardCode = async (data: Partial<CardCodeFormData> & { id: number }): Promise<CardCodeResponse> => {
  const url = await getApiUrl('apiCardCodeUpdateUrl');
  console.log('更新卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'updateCardCode',
    url,
    method: 'post',
    data,
    auth: true
  });
}

/**
 * 删除卡密
 */
export const deleteCardCode = async (id: number): Promise<CardCodeResponse> => {
  const url = await getApiUrl('apiCardCodeDeleteUrl');
  console.log('删除卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'deleteCardCode',
    url,
    method: 'post',
    data: { id },
    auth: true
  });
}

/**
 * 批量删除卡密
 */
export const batchDeleteCardCodes = async (ids: number[]): Promise<CardCodeBatchDeleteResponse> => {
  const url = await getApiUrl('apiCardCodeBatchDeleteUrl');
  console.log('批量删除卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'batchDeleteCardCodes',
    url,
    method: 'post',
    data: { ids },
    auth: true
  });
}

/**
 * 获取卡密详情
 */
export const getCardCodeDetail = async (id: number): Promise<CardCode> => {
  const url = await getApiUrl('apiCardCodeDetailUrl');
  console.log('获取卡密详情URL:', url)
  return sendRequestViaBackground({
    funName: 'getCardCodeDetail',
    url,
    method: 'get',
    params: { id },
    auth: true
  });
}

// 卡密统计参数
export interface CardCodeStatisticsParams {
  card_type?: number
  status?: number
  is_copied?: number
  copied_by?: number
  admin_id?: number
  vip_days_unit?: string // 卡密单位：year-年，month-月，day-天
  time_type?: string // 时间类型：created_at-创建时间，used_at-使用时间，copied_at-复制时间
  date_range?: string // 时间范围
  start_date?: string
  end_date?: string
}

// 卡密统计结果
export interface CardCodeStatistics {
  total_count: number
  unused_count: number
  used_count: number
  disabled_count: number
  vip_card_count: number
  points_card_count: number
  today_created: number
  today_used: number
  month_created: number
  month_used: number
  copied_count: number
  not_copied_count: number
  total_amount: number // 总金额
  unused_amount: number // 未使用金额
  used_amount: number // 已使用金额
  disabled_amount: number // 已禁用金额
  copied_amount: number // 已复制金额
  not_copied_amount: number // 未复制金额
}

/**
 * 获取卡密统计信息
 */
export const getCardCodeStatistics = async (filters: CardCodeStatisticsParams = {}): Promise<CardCodeStatistics> => {
  const url = await getApiUrl('apiCardCodeStatisticsUrl');
  console.log('获取卡密统计URL:', url)
  return sendRequestViaBackground({
    funName: 'getCardCodeStatistics',
    url,
    method: 'get',
    data: filters,
    auth: true
  });
}

/**
 * 卡密激活
 */
export const useCardCode = async (cardCode: string): Promise<any> => {
  const url = await getApiUrl('apiCardCodeUseCardUrl');
  console.log('使用卡密URL:', url)
  console.log('使用卡密参数:', { card_code: cardCode })
  return sendRequestViaBackground({
    funName: 'useCardCode',
    url,
    method: 'post',
    data: { card_code: cardCode },
    auth: true
  });
}

/**
 * 获取用户卡密激活记录
 */
export const getUserUsageRecords = async (params: {
  page?: number
  pageSize?: number
  card_type?: number
  start_date?: string
  end_date?: string
}): Promise<any> => {
  const url = await getApiUrl('apiCardCodeUserUsageRecordsUrl');
  console.log('获取用户使用记录URL:', url)
  return sendRequestViaBackground({
    funName: 'getUserUsageRecords',
    url,
    method: 'get',
    params,
    auth: true
  });
}

/**
 * 获取卡密激活记录列表（管理员查看）
 */
export const getUsageRecords = async (params: {
  page?: number
  pageSize?: number
  card_type?: number
  card_code?: string
  user_phone?: string
  start_date?: string
  end_date?: string
}): Promise<any> => {
  const url = await getApiUrl('apiCardCodeUsageRecordsUrl');
  console.log('获取使用记录列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getUsageRecords',
    url,
    method: 'get',
    params,
    auth: true
  });
}

/**
 * 获取卡密类型选项
 */
export const getCardTypeOptions = async (): Promise<any> => {
  const url = await getApiUrl('apiCardCodeCardTypeOptionsUrl');
  console.log('获取卡密类型选项URL:', url)
  return sendRequestViaBackground({
    funName: 'getCardTypeOptions',
    url,
    method: 'get',
    auth: true
  });
}

/**
 * 获取卡密状态选项
 */
export const getStatusOptions = async (): Promise<any> => {
  const url = await getApiUrl('apiCardCodeStatusOptionsUrl');
  console.log('获取卡密状态选项URL:', url)
  return sendRequestViaBackground({
    funName: 'getStatusOptions',
    url,
    method: 'get',
    auth: true
  });
}

// 卡密类型常量
export const CARD_TYPES = {
  VIP_CARD: 1,      // 有效期卡（包含积分和VIP有效期）
  POINTS_CARD: 2    // 纯积分卡（仅包含积分）
}

// 卡密状态常量
export const CARD_STATUS = {
  USED: 0,          // 已使用
  UNUSED: 1,        // 未使用
  DISABLED: 2       // 已禁用
}

// 卡密类型文本映射
export const CARD_TYPE_TEXT = {
  [CARD_TYPES.VIP_CARD]: '有效期卡',
  [CARD_TYPES.POINTS_CARD]: '积分卡'
}

// 卡密状态文本映射
export const CARD_STATUS_TEXT = {
  [CARD_STATUS.USED]: '已使用',
  [CARD_STATUS.UNUSED]: '未使用',
  [CARD_STATUS.DISABLED]: '已禁用'
}

// 获取状态标签类型
export const getStatusTagType = (status: number): string => {
  switch (status) {
    case CARD_STATUS.USED:
      return 'info'
    case CARD_STATUS.UNUSED:
      return 'success'
    case CARD_STATUS.DISABLED:
      return 'danger'
    default:
      return 'info'
  }
}

// 格式化价格
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`
}

// 格式化积分
export const formatPoints = (points: number): string => {
  if (points >= 10000) {
    return `${(points / 10000).toFixed(1)}万`
  }
  return points.toString()
}

// 验证卡密格式
export const validateCardCode = (cardCode: string): boolean => {
  // 卡密格式：前缀-随机字符串，总长度≤50字符
  const regex = /^[a-zA-Z][a-zA-Z0-9]*-[A-Z0-9]+$/
  return regex.test(cardCode) && cardCode.length <= 50
}

// 验证前缀格式
export const validatePrefix = (prefix: string): boolean => {
  // 前缀：最多3字符，不能以数字0开始
  const regex = /^[a-zA-Z][a-zA-Z0-9]{0,2}$/
  return regex.test(prefix)
}

// 生成批次号
export const generateBatchNo = (): string => {
  const now = new Date()
  const timestamp = now.getFullYear().toString() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0') +
    now.getHours().toString().padStart(2, '0') +
    now.getMinutes().toString().padStart(2, '0') +
    now.getSeconds().toString().padStart(2, '0')
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `BATCH_${timestamp}_${random}`
}

// 计算使用率
export const calculateUsageRate = (used: number, total: number): number => {
  if (total === 0) return 0
  return Math.round((used / total) * 100)
}

// 格式化日期时间
export const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化日期
export const formatDate = (date: string): string => {
  if (!date) return '-'
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 检查卡密是否过期
export const isCardExpired = (validUntil: string): boolean => {
  if (!validUntil) return false
  return new Date(validUntil) < new Date()
}

// 获取卡密剩余有效天数
export const getRemainingDays = (validUntil: string): number => {
  if (!validUntil) return -1 // -1表示永久有效
  const now = new Date()
  const expiry = new Date(validUntil)
  const diffTime = expiry.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

/**
 * 复制卡密
 */
export const copyCardCode = async (id: number, memo?: string): Promise<any> => {
  const url = await getApiUrl('apiCardCodeCopyUrl');
  console.log('复制卡密URL:', url)
  return sendRequestViaBackground({
    funName: 'copyCardCode',
    url,
    method: 'post',
    data: { id, memo },
    auth: true
  });
}

/**
 * 获取VIP时长单位选项
 */
export const getVipDaysUnitOptions = async (): Promise<any> => {
  const url = await getApiUrl('apiCardCodeVipDaysUnitOptionsUrl');
  console.log('获取VIP时长单位选项URL:', url)
  return sendRequestViaBackground({
    funName: 'getVipDaysUnitOptions',
    url,
    method: 'get',
    auth: true
  });
}

/**
 * 获取复制状态选项
 */
export const getCopyStatusOptions = async (): Promise<any> => {
  const url = await getApiUrl('apiCardCodeCopyStatusOptionsUrl');
  console.log('获取复制状态选项URL:', url)
  return sendRequestViaBackground({
    funName: 'getCopyStatusOptions',
    url,
    method: 'get',
    auth: true
  });
}

/**
 * 获取成员选项
 */
export const getMemberOptions = async (): Promise<any> => {
  const url = await getApiUrl('apiCardCodeMemberOptionsUrl');
  console.log('获取成员选项URL:', url)
  return sendRequestViaBackground({
    funName: 'getMemberOptions',
    url,
    method: 'get',
    auth: true
  });
}
