<?php

namespace App\Utils;

/**
 * 版本比较工具类
 * 
 * 用于处理三段式版本号（x.y.z格式）的解析、验证和比较
 * 支持复杂的版本号比较逻辑，如 2.0.99 < 2.1.3
 */
class VersionComparator
{
    /**
     * 版本号格式正则表达式
     * 匹配 x.y.z 格式，其中 x、y、z 都是数字
     */
    private const VERSION_PATTERN = '/^(\d+)\.(\d+)\.(\d+)$/';

    /**
     * 验证版本号格式是否正确
     *
     * @param string $version 版本号字符串
     * @return bool 格式是否正确
     */
    public static function isValidFormat(string $version): bool
    {
        return preg_match(self::VERSION_PATTERN, trim($version)) === 1;
    }

    /**
     * 解析版本号字符串为数组
     *
     * @param string $version 版本号字符串
     * @return array|null 解析结果 ['major' => int, 'minor' => int, 'patch' => int] 或 null
     */
    public static function parseVersion(string $version): ?array
    {
        $version = trim($version);
        
        if (!self::isValidFormat($version)) {
            return null;
        }

        preg_match(self::VERSION_PATTERN, $version, $matches);
        
        return [
            'major' => (int) $matches[1],
            'minor' => (int) $matches[2],
            'patch' => (int) $matches[3],
            'raw' => $version
        ];
    }

    /**
     * 比较两个版本号
     *
     * @param string $version1 第一个版本号
     * @param string $version2 第二个版本号
     * @return int 比较结果：-1 表示 version1 < version2，0 表示相等，1 表示 version1 > version2
     * @throws \InvalidArgumentException 当版本号格式不正确时抛出异常
     */
    public static function compare(string $version1, string $version2): int
    {
        $parsed1 = self::parseVersion($version1);
        $parsed2 = self::parseVersion($version2);

        if ($parsed1 === null) {
            throw new \InvalidArgumentException("Invalid version format: {$version1}");
        }

        if ($parsed2 === null) {
            throw new \InvalidArgumentException("Invalid version format: {$version2}");
        }

        // 比较主版本号
        if ($parsed1['major'] !== $parsed2['major']) {
            return $parsed1['major'] <=> $parsed2['major'];
        }

        // 比较次版本号
        if ($parsed1['minor'] !== $parsed2['minor']) {
            return $parsed1['minor'] <=> $parsed2['minor'];
        }

        // 比较修订号
        return $parsed1['patch'] <=> $parsed2['patch'];
    }

    /**
     * 检查版本号是否小于指定版本
     *
     * @param string $version 要检查的版本号
     * @param string $compareVersion 比较的版本号
     * @return bool
     */
    public static function isLowerThan(string $version, string $compareVersion): bool
    {
        return self::compare($version, $compareVersion) < 0;
    }

    /**
     * 检查版本号是否大于指定版本
     *
     * @param string $version 要检查的版本号
     * @param string $compareVersion 比较的版本号
     * @return bool
     */
    public static function isGreaterThan(string $version, string $compareVersion): bool
    {
        return self::compare($version, $compareVersion) > 0;
    }

    /**
     * 检查版本号是否等于指定版本
     *
     * @param string $version 要检查的版本号
     * @param string $compareVersion 比较的版本号
     * @return bool
     */
    public static function isEqualTo(string $version, string $compareVersion): bool
    {
        return self::compare($version, $compareVersion) === 0;
    }

    /**
     * 检查版本号是否在指定范围内（包含边界）
     *
     * @param string $version 要检查的版本号
     * @param string $minVersion 最小版本号
     * @param string $maxVersion 最大版本号
     * @return bool
     */
    public static function isInRange(string $version, string $minVersion, string $maxVersion): bool
    {
        return self::compare($version, $minVersion) >= 0 && self::compare($version, $maxVersion) <= 0;
    }

    /**
     * 获取版本号的字符串表示（用于调试）
     *
     * @param string $version 版本号
     * @return string 格式化的版本信息
     */
    public static function getVersionInfo(string $version): string
    {
        $parsed = self::parseVersion($version);
        
        if ($parsed === null) {
            return "Invalid version format: {$version}";
        }

        return sprintf(
            "Version %s (Major: %d, Minor: %d, Patch: %d)",
            $parsed['raw'],
            $parsed['major'],
            $parsed['minor'],
            $parsed['patch']
        );
    }

    /**
     * 批量验证版本号格式
     *
     * @param array $versions 版本号数组
     * @return array 验证结果 ['valid' => [], 'invalid' => []]
     */
    public static function validateMultiple(array $versions): array
    {
        $result = ['valid' => [], 'invalid' => []];

        foreach ($versions as $version) {
            if (self::isValidFormat($version)) {
                $result['valid'][] = $version;
            } else {
                $result['invalid'][] = $version;
            }
        }

        return $result;
    }
}