<?php
declare(strict_types=1);

use App\Http\Controllers\Api\V1\Wap;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::namespace('Api')->group(function () {
    Route::get('captcha',[Wap\Common\CaptchaController::class,'getCaptcha']);
    Route::post('login/loginSms',[Wap\User\UserController::class,'loginSms']);
    Route::post('user/register',[Wap\User\UserController::class,'register']);
    Route::get('index',[Wap\Index\IndexController::class,'index']);
});

Route::namespace('Api')->middleware('userCanEmptyLogin')->group(function(){
    Route::get('services',[Wap\Index\IndexController::class,'getServices']);
});

// Temu 页面相关路由（不需要版本验证）
Route::namespace('Api')->middleware('userLogin')->group(function(){
    Route::post('temu/goodsAdd',[Wap\TeMu\GoodsController::class,'goodsAdd']);
    Route::post('temu/checkGoodsExists',[Wap\TeMu\GoodsController::class,'checkGoodsExists']);
    Route::post('temu/batchCheckGoodsExists',[Wap\TeMu\GoodsController::class,'batchCheckGoodsExists']);
});

// Web 项目相关路由（需要版本验证）
Route::namespace('Api')->middleware(['userLogin'])->group(function(){
    // 店铺管理路由组（需要VIP权限）
    Route::prefix('store')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('list',[Wap\User\StoreController::class,'list']);//获取店铺列表
        Route::post('create',[Wap\User\StoreController::class,'create']);//创建店铺
        Route::post('update',[Wap\User\StoreController::class,'update']);//更新店铺
        Route::post('batch-update',[Wap\User\StoreController::class,'batchUpdate']);//批量更新店铺
        Route::post('delete',[Wap\User\StoreController::class,'delete']);//删除店铺
        Route::get('detail',[Wap\User\StoreController::class,'detail']);//获取店铺详情
    });
    
    // 商品管理路由组（需要VIP权限）
    Route::prefix('goods')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('list',[Wap\User\GoodsController::class,'list']);//获取商品列表
        Route::post('batch-update',[Wap\User\GoodsController::class,'batchUpdate']);//批量更新商品
        Route::get('need-image-process',[Wap\User\GoodsController::class,'getNeedImageProcessGoods']);//获取需要处理图片的商品ID列表
        Route::get('statistics',[Wap\User\GoodsController::class,'getGoodsStatistics']);//获取商品统计信息
        Route::post('adjust-sku-price',[Wap\User\GoodsController::class,'adjustSkuPrice']);//调整SKU价格
        Route::get('price-adjustment-logs',[Wap\User\GoodsController::class,'getPriceAdjustmentLogs']);//获取价格调整日志
    });

    // 子账号商品管理路由组（需要VIP权限，主账号和子账号均可访问）
    Route::prefix('sub-account-goods')->middleware(['versionValidation','vipCheck'])->group(function(){
        Route::get('list',[Wap\User\SubAccountGoodsController::class,'list']);//获取子账号商品列表
        Route::post('delete',[Wap\User\SubAccountGoodsController::class,'delete']);//子账号删除商品
    });

    // 商品分类关联管理路由组（需要VIP权限）
    Route::prefix('goods_cat_relation')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('get',[Wap\User\UserGoodsCatRelationController::class,'getGoodsCatRelation']);//获取商品分类关联
        Route::post('save',[Wap\User\UserGoodsCatRelationController::class,'saveGoodsCatRelation']);//保存商品分类关联
        Route::post('delete',[Wap\User\UserGoodsCatRelationController::class,'deleteGoodsCatRelation']);//删除商品分类关联
        Route::post('batch-get',[Wap\User\UserGoodsCatRelationController::class,'batchGetGoodsCatRelations']);//批量获取商品分类关联
    });

    // 商品目录管理路由组（需要VIP权限）
    Route::prefix('goods_directory')->middleware(['versionValidation','vipCheck'])->group(function(){
        Route::get('list',[Wap\User\UserGoodsDirectoryController::class,'list']);//获取目录列表
        Route::post('create',[Wap\User\UserGoodsDirectoryController::class,'create']);//创建目录
        Route::post('update',[Wap\User\UserGoodsDirectoryController::class,'update']);//更新目录
        Route::post('delete',[Wap\User\UserGoodsDirectoryController::class,'delete']);//删除目录
        Route::post('batch-update',[Wap\User\UserGoodsDirectoryController::class,'batchUpdate']);//批量更新目录
        Route::get('detail',[Wap\User\UserGoodsDirectoryController::class,'detail']);//获取目录详情
        //Route::post('update-goods-count',[Wap\User\UserGoodsDirectoryController::class,'updateGoodsCount']);//更新目录商品数量
        //Route::post('update-all-goods-count',[Wap\User\UserGoodsDirectoryController::class,'updateAllGoodsCount']);//批量更新所有目录商品数量
    });

    Route::prefix('card_code')->group(function(){
        Route::post('use-card',[Wap\User\CardCodeController::class,'useCard']);//卡密激活
        Route::get('user-usage-records',[Wap\User\CardCodeController::class,'userUsageRecords']);//获取用户卡密激活记录
    });


    // 卡密管理路由组
    Route::prefix('card_code')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('list',[Wap\User\CardCodeController::class,'list']);//获取卡密列表
        Route::post('create',[Wap\User\CardCodeController::class,'create']);//创建单个卡密
        Route::post('batch-create',[Wap\User\CardCodeController::class,'batchCreate']);//批量生成卡密
        Route::post('update',[Wap\User\CardCodeController::class,'update']);//更新卡密
        Route::post('delete',[Wap\User\CardCodeController::class,'delete']);//删除卡密
        Route::post('batch-delete',[Wap\User\CardCodeController::class,'batchDelete']);//批量删除卡密
        Route::get('detail',[Wap\User\CardCodeController::class,'detail']);//获取卡密详情
        Route::get('statistics',[Wap\User\CardCodeController::class,'statistics']);//获取卡密统计信息
        Route::post('copy',[Wap\User\CardCodeController::class,'copy']);//复制卡密
        Route::get('vip-days-unit-options',[Wap\User\CardCodeController::class,'vipDaysUnitOptions']);//获取VIP时长单位选项
        Route::get('copy-status-options',[Wap\User\CardCodeController::class,'copyStatusOptions']);//获取复制状态选项
        Route::get('member-options',[Wap\User\CardCodeController::class,'memberOptions']);//获取成员选项
        Route::get('usage-records',[Wap\User\CardCodeController::class,'usageRecords']);//获取卡密激活记录列表（管理员查看）
        Route::get('card-type-options',[Wap\User\CardCodeController::class,'cardTypeOptions']);//获取卡密类型选项
        Route::get('status-options',[Wap\User\CardCodeController::class,'statusOptions']);//获取卡密状态选项
    });

    // AI Key管理路由组（需要VIP权限）
    Route::prefix('ai_key')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('list',[Wap\User\UserAiKeyController::class,'list']);//获取AI Key列表
        Route::post('create',[Wap\User\UserAiKeyController::class,'create']);//创建AI Key
        Route::post('update',[Wap\User\UserAiKeyController::class,'update']);//更新AI Key
        Route::post('delete',[Wap\User\UserAiKeyController::class,'delete']);//删除AI Key
        Route::post('batch-update',[Wap\User\UserAiKeyController::class,'batchUpdate']);//批量更新AI Key
        Route::get('detail',[Wap\User\UserAiKeyController::class,'detail']);//获取AI Key详情
    });

    Route::prefix('goods_category')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('cat_temu_webpage_main',[Wap\User\GoodsCategoryController::class,'catTemuWebpageMain']);//获取temu商品网页前端主分类列表
        Route::get('cat_temu_webpage_list',[Wap\User\GoodsCategoryController::class,'catTemuWebpageList']);//获取temu网页前端商品分类列表
        Route::get('cat_temu_detail',[Wap\User\GoodsCategoryController::class,'catTemuDetail']);//获取temu网页前端商品分类详情
        Route::get('cat_temu_main',[Wap\User\GoodsCategoryController::class,'catTemuMain']);//获取temu商品主分类列表
        Route::get('cat_temu_list',[Wap\User\GoodsCategoryController::class,'catTemuList']);//获取temu商品分类列表
        Route::get('cat_temu_list_lazy',[Wap\User\GoodsCategoryController::class,'catTemuListLazy']);//获取temu商品分类列表（懒加载）
        Route::get('cat_temu_list_flat',[Wap\User\GoodsCategoryController::class,'catTemuListFlat']);//获取temu商品分类列表（虚拟表格专用）
        Route::get('cat_temu_children_count',[Wap\User\GoodsCategoryController::class,'catTemuChildrenCount']);//获取分类子分类数量
        Route::get('cat_temu_children_counts',[Wap\User\GoodsCategoryController::class,'catTemuChildrenCounts']);//批量获取分类子分类数量
        Route::get('cat_n11_list',[Wap\User\GoodsCategoryController::class,'catN11List']);//获取n11商品分类
        Route::get('cat_n11_detail',[Wap\User\GoodsCategoryController::class,'catN11Detail']);//获取n11商品分类详情
        Route::post('cat_n11_update',[Wap\User\GoodsCategoryController::class,'catN11Update']);//更新n11商品分类
        Route::post('cat_relation_set',[Wap\User\GoodsCategoryController::class,'catRelationSet']);//分类关联设置
        Route::get('cat_relation_list',[Wap\User\GoodsCategoryController::class,'catRelationList']);//获取分类关联列表
    });
    
    Route::prefix('user')->group(function(){
        Route::get('info',[Wap\User\UserController::class,'getUserInfo']);
        Route::get('store_info',[Wap\User\UserController::class,'getStoreInfo']);
        Route::get('logout',[Wap\User\UserController::class,'logout']);
        Route::post('change-password',[Wap\User\UserController::class,'changePassword']);//修改密码
        Route::post('generate-api-credentials',[Wap\User\ApiCredentialController::class,'generateCredentials'])->middleware('denySubAccount');//生成API凭证
    });

     Route::prefix('user')->middleware(['vipCheck'])->group(function(){
        // 商品采集设置相关路由
        Route::get('collection-settings',[Wap\User\UserGoodsCollectionSettingsController::class,'getSettings']);//获取采集设置
        Route::post('collection-settings',[Wap\User\UserGoodsCollectionSettingsController::class,'saveSettings']);//保存采集设置
        Route::get('available-directories',[Wap\User\UserGoodsCollectionSettingsController::class,'getAvailableDirectories']);//获取可用目录
        Route::get('check-need-setup',[Wap\User\UserGoodsCollectionSettingsController::class,'checkNeedSetup']);//检查是否需要设置
     });

    // 用户任务管理路由组（需要VIP权限）
    Route::prefix('user')->middleware(['vipCheck','denySubAccount'])->group(function(){
        Route::post('task/add',[Wap\User\UserTaskController::class,'addTask']);//添加任务
        Route::get('task/list',[Wap\User\UserTaskController::class,'list']);//获取任务列表
        Route::get('task/start',[Wap\User\UserTaskController::class,'startTask']);//开启任务
        Route::post('task/update',[Wap\User\UserTaskController::class,'updateTask']);//更新任务
        Route::get('task/detail',[Wap\User\UserTaskDetailController::class,'getTaskDetail']);//获取任务详情
        Route::get('task/detail-list',[Wap\User\UserTaskDetailController::class,'getTaskDetailList']);//获取任务详情列表
        Route::get('task/detail-statistics',[Wap\User\UserTaskDetailController::class,'getTaskDetailStatistics']);//获取任务详情统计
        Route::post('task/detail/batch-retry',[Wap\User\UserTaskDetailController::class,'batchRetryFailedTasks']);//批量重试失败任务
        Route::post('task/detail/query-results',[Wap\User\UserTaskDetailController::class,'queryTaskResults']);//查询任务结果
        Route::get('task/detail/pending-query',[Wap\User\UserTaskDetailController::class,'getPendingQueryTasks']);//获取待查询任务列表
        Route::post('task/detail/batch-update',[Wap\User\UserTaskDetailController::class,'batchUpdateTaskDetails']);//批量更新任务状态
        Route::post('task/detail/save-upload-params',[Wap\User\UserTaskDetailController::class,'saveUploadParams']);//保存上传参数
        Route::get('task/detail/retry-upload-params',[Wap\User\UserTaskDetailController::class,'getRetryUploadParams']);//获取重新上传参数-批量重传时的商品所有数据信息
        Route::post('task/detail/batch-retry-upload',[Wap\User\UserTaskDetailController::class,'batchRetryUpload']);//批量重新上传失败任务
        Route::post('task/detail/batch-retry-upload-by-status',[Wap\User\UserTaskDetailController::class,'batchRetryUploadByStatus']);//按状态批量重新上传任务【使用这个】
        Route::get('task/detail/find-by-stock-code',[Wap\User\UserTaskDetailController::class,'findByStockCode']);//根据stockCode查找任务详情
        Route::post('task/update-category',[Wap\User\UserTaskController::class,'updateTaskCategory']);//更新任务分类

        // 图片处理相关路由
        Route::post('process-goods-images',[Wap\User\ImageProcessController::class,'processGoodsImages']);//处理商品图片本地化

        // AI改写任务相关路由
        Route::post('task-new/generate-details',[Wap\User\UserTaskNewController::class,'generateTaskDetails']);//生成任务详情记录
        Route::get('task-new/next-ai-task',[Wap\User\UserTaskNewController::class,'getNextAiTask']);//获取下一个待AI改写的任务详情
        Route::post('task-new/update-ai-result',[Wap\User\UserTaskNewController::class,'updateAiResult']);//更新任务详情AI改写结果
        Route::get('task-new/random-ai-key',[Wap\User\UserTaskNewController::class,'getRandomAiKey']);//获取用户可用的AI Key
        Route::get('task-new/ai-progress',[Wap\User\UserTaskNewController::class,'getAiProgress']);//获取任务AI改写进度
        Route::post('task-new/complete-ai-task',[Wap\User\UserTaskNewController::class,'completeAiTask']);//完成任务AI改写

        // 积分相关路由  暂不启用
        Route::get('points/logs',[Wap\User\UserPointsController::class,'getPointsLogs']);//获取积分使用记录
        //Route::get('points/check-task-deducted',[Wap\User\UserPointsController::class,'checkTaskPointsDeducted']);//检查任务是否已扣除积分
    });

    // N11重新上传商品管理路由组（需要VIP权限）
    Route::prefix('n11/rejected-products')->middleware(['vipCheck','denySubAccount'])->group(function(){
        Route::post('batch-save', [Wap\N11\N11RejectedProductController::class, 'batchSave']); // 批量保存重新上传商品
        Route::get('pending-count', [Wap\N11\N11RejectedProductController::class, 'getPendingCount']); // 获取待处理数量
        Route::get('next-pending', [Wap\N11\N11RejectedProductController::class, 'getNextPending']); // 获取下一个待处理商品
        Route::post('update-status', [Wap\N11\N11RejectedProductController::class, 'updateStatus']); // 更新处理状态
        Route::post('batch-update-status', [Wap\N11\N11RejectedProductController::class, 'batchUpdateStatus']); // 批量更新商品状态为拒绝状态
        Route::get('statistics', [Wap\N11\N11RejectedProductController::class, 'getStatistics']); // 获取统计信息
    });

    // 子账号管理路由组（需要VIP权限，仅主账号可访问）
    Route::prefix('sub-account')->middleware(['versionValidation','vipCheck','denySubAccount'])->group(function(){
        Route::get('list', [Wap\User\SubAccountController::class, 'list']); // 获取子账号列表
        Route::post('create', [Wap\User\SubAccountController::class, 'create']); // 创建子账号
        Route::post('update', [Wap\User\SubAccountController::class, 'update']); // 更新子账号
        Route::post('delete', [Wap\User\SubAccountController::class, 'delete']); // 删除子账号
        Route::post('toggle-status', [Wap\User\SubAccountController::class, 'toggleStatus']); // 启用/禁用子账号
        Route::get('detail', [Wap\User\SubAccountController::class, 'detail']); // 获取子账号详情
    });

});