import { ElMessage, ElNotification } from 'element-plus';
import * as utils_new from '@/utils/new/utils';

interface ApiResponseData<T = any> {
  status: number;
  code: number;
  msg?: string;
  data: T;
}

/**
 * 清除所有登录相关的存储数据
 */
const clearLoginData = async (): Promise<void> => {
  try {
    // 清除同步存储中的登录信息
    await utils_new.setSyncStorageData({
      is_login: false
    });

    // 清除其他同步存储的用户信息
    chrome.storage.sync.remove(['phone', 'expiryDate', 'isVip']);

    // 清除本地存储的token
    chrome.storage.local.remove(['token']);

    console.log('登录数据已清除');
  } catch (error) {
    console.error('清除登录数据失败:', error);
  }
};

/**
 * 发送登录失效消息到所有相关页面
 */
const notifyLoginExpired = (): void => {
  try {
    // 发送消息到所有标签页的content scripts
    chrome.tabs.query({}, (tabs: any) => {
      tabs.forEach((tab: any) => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            type: 'LOGIN_EXPIRED',
            timestamp: Date.now()
          }).catch(() => {
            // 忽略无法发送消息的标签页（如chrome://页面）
          });
        }
      });
    });

    // 发送消息到background script
    chrome.runtime.sendMessage({
      type: 'LOGIN_EXPIRED',
      timestamp: Date.now()
    }).catch(() => {
      // 忽略发送失败的情况
    });

    console.log('登录失效通知已发送');
  } catch (error) {
    console.error('发送登录失效通知失败:', error);
  }
};

/**
 * 处理 API 响应，检查错误状态并触发通知。
 * @param responseData 接口返回的数据，通常是 response[0].data
 * @param notificationType 通知类型，可选 'message' 或 'notification'，默认为 'notification'
 */
export const handleApiResponse = <T>(responseData: ApiResponseData<T>, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  // 检查是否存在响应数据且 status 不是 200
  console.log('responseData', responseData);

  // 检查登录失效（401状态码）
  if (responseData && responseData.status === 401) {
    console.log('检测到登录失效，状态码401');

    // 清除登录数据
    clearLoginData();

    // 发送登录失效通知
    notifyLoginExpired();

    // 显示登录失效提示
    if (notificationType === 'notification') {
      ElNotification({
        title: '登录失效',
        message: '您的登录已失效，请重新登录',
        type: 'warning',
        duration: 5000,
      });
    } else {
      ElMessage({
        message: '您的登录已失效，请重新登录',
        type: 'warning',
      });
    }

    // 返回 false 表示处理了错误
    return false;
  }

  // 检查VIP权限不足（403状态码且code为1003）
  if (responseData && responseData.status === 403 && responseData.code === 1003) {
    console.log('检测到VIP权限不足，状态码403，错误码1003');

    // 显示VIP权限不足提示
    if (notificationType === 'notification') {
      ElNotification({
        title: 'VIP权限不足',
        message: responseData.msg || '您不是VIP会员或积分不足，请升级会员后使用',
        type: 'warning',
        duration: 3000,
      });
    } else {
      ElMessage({
        message: responseData.msg || '您不是VIP会员或积分不足，请升级会员后使用',
        type: 'warning',
      });
    }

    // 通知popup页面跳转到卡密激活页面
    chrome.runtime.sendMessage({
      type: 'VIP_PERMISSION_REQUIRED',
      message: responseData.msg || '您不是VIP会员或积分不足，请升级会员后使用'
    });

    // 返回 false 表示处理了错误
    return false;
  }

  if (responseData && responseData.code != 1 && responseData.msg) {
    const message = `错误码: ${responseData.status}, 消息: ${responseData.msg}`;
    if (notificationType === 'notification') {
      ElNotification({
        title: '错误提示',
        message: responseData.msg,
        type: 'error',
        duration: 0, // 常驻通知
      });
    } else {
      ElMessage({
        message: responseData.msg,
        type: 'error',
      });
    }

    // 返回 false 表示处理了错误
    return false;
  }
  // 返回 true 表示响应正常或未处理错误
  return true;
};
