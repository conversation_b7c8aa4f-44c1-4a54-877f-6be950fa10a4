/**
 * N11商品上传API - Chrome扩展版本
 * 用于处理N11平台的商品上传相关接口
 */

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(request: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: request.url,
      method: request.method || 'get',
      pramas: request.data || request.params || {},
      headers: request.headers || {},
      auth: request.auth || false,
      encrypto: request.encrypto || false,
      timeout: request.timeout || 59000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      // 对于N11 API，直接返回响应数据
      if (response && response[0]) {
        resolve(response[0].data || response[0]);
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// N11分类属性接口
export interface N11CategoryAttribute {
  attributeId: number
  categoryId: number
  attributeName: string
  isMandatory: boolean
  isVariant: boolean
  isSlicer: boolean
  isCustomValue: boolean
  isN11Grouping: boolean
  attributeOrder: number
  attributeValues: Array<{
    id: number
    value: string
  }>
}

// N11分类响应接口
export interface N11CategoryResponse {
  id: number
  name: string
  categoryAttributes: N11CategoryAttribute[]
}

// 商品属性接口
export interface ProductAttribute {
  id: number
  valueId: number | null
  customValue: string | null
}

// 商品数据接口
export interface ProductData {
  title: string
  description: string
  categoryId: number
  currencyType: string
  productMainId: string
  preparingDay: number
  shipmentTemplate: string
  stockCode: string
  quantity: number
  salePrice: number
  listPrice: number
  vatRate: number
  images: Array<{
    url: string
    order: number
  }>
  attributes: ProductAttribute[]
}

// N11商品上传响应接口
export interface N11ProductUploadResponse {
  id?: number
  type?: string
  status?: string
  reasons?: string[]
  error?: string
  response?: string
}

/**
 * 获取N11分类属性
 * @param categoryId 分类ID
 * @param appKey N11应用密钥
 * @returns 分类属性数据
 */
export const getN11CategoryAttributes = async (
  categoryId: number,
  appKey: string
): Promise<N11CategoryResponse> => {
  const url = `https://api.n11.com/cdn/category/${categoryId}/attribute`

  const headers = {
    "Content-Type": "text/json; charset=utf-8",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "appkey": appKey
  }

  try {
    const response = await sendRequestViaBackground({
      funName: 'getN11CategoryAttributes',
      url,
      method: 'get',
      headers,
      auth: false,
      encrypto: false,
      timeout: 60000
    })

    console.log('N11分类属性响应:', response)

    // 检查响应是否为空或null
    if (!response || response === null || response === undefined) {
      throw new Error('商品属性获取失败')
    }

    // 检查响应是否包含必要的数据结构
    if (typeof response !== 'object') {
      throw new Error('商品属性获取失败')
    }

    // 检查是否包含categoryAttributes字段
    if (!response.categoryAttributes || !Array.isArray(response.categoryAttributes)) {
      console.warn('N11分类属性响应缺少categoryAttributes字段:', response)
      throw new Error('商品属性获取失败')
    }

    // 检查categoryAttributes是否为空数组
    if (response.categoryAttributes.length === 0) {
      console.warn('N11分类属性响应categoryAttributes为空数组:', response)
      throw new Error('商品属性获取失败')
    }

    return response as N11CategoryResponse

  } catch (error: any) {
    console.error('获取N11分类属性失败:', error)

    // 如果是我们自定义的友好错误信息，直接抛出
    if (error.message === '商品属性获取失败') {
      throw error
    }

    // 对于其他错误，也统一显示友好信息
    throw new Error('商品属性获取失败')
  }
}

/**
 * 上传商品到N11平台
 * @param productData 商品数据
 * @param appKey N11应用密钥
 * @param appSecret N11应用秘钥
 * @param integratorName 集成商名称
 * @param taskDetailId 任务详情ID（用于保存参数）
 * @param debugMode 调试模式，true为调试模式（不真正调用API）
 * @returns 上传结果
 */
export const uploadProductToN11 = async (
  productData: ProductData,
  appKey: string,
  appSecret: string,
  integratorName: string,
  taskDetailId?: number,
  debugMode?: boolean
): Promise<N11ProductUploadResponse> => {
  const url = "https://api.n11.com/ms/product/tasks/product-create"

  const payload = {
    payload: {
      integrator: integratorName,
      skus: [productData]
    }
  }

  const headers = {
    "Content-Type": "application/json",
    "appkey": appKey,
    "appsecret": appSecret
  }

  // 构建请求信息用于调试和保存
  const requestInfo = {
    url,
    method: 'post',
    headers,
    payload,
    timestamp: new Date().toISOString(),
    taskDetailId
  }

  // 确定实际的调试模式
  const actualDebugMode = debugMode ?? true // Chrome扩展默认开启调试模式

  console.log("------------------上传商品到N11调试数据开始---------------------");
  console.log('请求URL:', url);
  console.log('请求方式:', 'post');
  console.log('请求头:', headers);
  console.log('请求载荷:', payload);
  console.log('调试模式:', actualDebugMode);
  console.log("------------------上传商品到N11调试数据结束---------------------");

  // 保存上传参数到数据库（无论调试模式是否开启）
  /* if (taskDetailId) {
    try {
      // 在Chrome扩展中，我们可以保存到localStorage或通过API保存
      const { saveUploadParams } = await import('./taskApi')
      await saveUploadParams(taskDetailId, 'n11', JSON.stringify(requestInfo))
      console.log('上传参数已保存到数据库')
    } catch (error) {
      console.error('保存上传参数失败:', error)
      // 不影响主流程，继续执行
    }
  } */

  // 调试模式：返回模拟成功响应
  if (actualDebugMode) {
    console.log('调试模式开启，返回模拟成功响应')
    return {
      id: Math.floor(Math.random() * 1000000),
      type: 'PRODUCT_CREATE',
      status: 'SUCCESS',
      reasons: ['调试模式：商品上传成功']
    } as N11ProductUploadResponse
  }

  // 正常模式：真正调用N11 API
  try {
    const response = await sendRequestViaBackground({
      funName: 'uploadProductToN11',
      url,
      method: 'post',
      headers,
      data: JSON.stringify(payload),
      auth: false,
      encrypto: false,
      timeout: 60000,
      addVersion:false
    })

    console.log('N11商品上传响应:', response)
    console.log("------------------上传商品到N11响应数据开始---------------------");
    console.log(response);
    console.log("------------------上传商品到N11响应数据结束---------------------");

    // 成功响应格式处理
    if (response && response.id) {
      return {
        id: response.id,
        type: response.type,
        status: response.status,
        reasons: response.reasons
      } as N11ProductUploadResponse
    } else {
      // 如果没有id但有响应，可能是其他格式的成功响应
      return response as N11ProductUploadResponse
    }

  } catch (error: any) {
    console.error('N11商品上传失败:', error)

    // 处理错误信息
    let errorMessage = '上传失败'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response && error.response.data) {
      errorMessage = JSON.stringify(error.response.data)
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    console.log("------------------上传商品到N11错误数据开始---------------------");
    console.log('错误信息:', errorMessage);
    console.log('完整错误对象:', error);
    console.log("------------------上传商品到N11错误数据结束---------------------");

    // 返回失败格式的响应
    return {
      error: errorMessage,
      response: JSON.stringify(error)
    } as N11ProductUploadResponse
  }
}

/**
 * 处理商品属性，根据分类属性和商品信息生成属性列表
 * @param categoryAttributes 分类属性列表
 * @param specKeyValues 商品规格键值对字符串
 * @param brandName 品牌名称
 * @returns 处理后的属性列表
 */
export const processProductAttributes = (
  categoryAttributes: N11CategoryAttribute[],
  specKeyValues: string,
  brandName: string
): ProductAttribute[] => {
  const attributes: ProductAttribute[] = []

  // 检查分类属性是否有效
  if (!categoryAttributes || !Array.isArray(categoryAttributes)) {
    console.warn('分类属性数据无效，返回空属性列表')
    return attributes
  }

  // 解析规格键值对
  const specMap = new Map<string, string>()
  if (specKeyValues) {
    const specs = specKeyValues.split(',')
    specs.forEach(spec => {
      const [key, value] = spec.split(':')
      if (key && value) {
        specMap.set(key.trim().toLowerCase(), value.trim())
      }
    })
  }

  // 处理每个分类属性
  categoryAttributes.forEach(attr => {
    const attrName = attr.attributeName.toLowerCase()

    // 处理品牌属性
    if (attrName.includes('marka') || attrName.includes('brand')) {
      if (brandName && attr.attributeValues && Array.isArray(attr.attributeValues)) {
        // 查找匹配的品牌值
        const brandValue = attr.attributeValues.find(v =>
          v.value.toLowerCase() === brandName.toLowerCase()
        )

        if (brandValue) {
          attributes.push({
            id: attr.attributeId,
            valueId: brandValue.id,
            customValue: null
          })
        } else if (attr.isCustomValue) {
          // 如果支持自定义值，使用自定义品牌名
          attributes.push({
            id: attr.attributeId,
            valueId: null,
            customValue: brandName
          })
        }
      }
      return
    }

    // 处理其他属性
    const specValue = specMap.get(attrName)
    if (specValue) {
      // 查找匹配的属性值
      if (attr.attributeValues && Array.isArray(attr.attributeValues)) {
        const matchedValue = attr.attributeValues.find(v =>
          v.value.toLowerCase() === specValue.toLowerCase()
        )

        if (matchedValue) {
          attributes.push({
            id: attr.attributeId,
            valueId: matchedValue.id,
            customValue: null
          })
        } else if (attr.isCustomValue) {
          // 如果支持自定义值，使用自定义值
          attributes.push({
            id: attr.attributeId,
            valueId: null,
            customValue: specValue
          })
        }
      } else if (attr.isCustomValue) {
        // 如果没有预定义值但支持自定义，使用自定义值
        attributes.push({
          id: attr.attributeId,
          valueId: null,
          customValue: specValue
        })
      }
    } else if (attr.isMandatory && attr.attributeValues && Array.isArray(attr.attributeValues) && attr.attributeValues.length > 0) {
      // 必填属性但没有匹配值时，使用第一个可用值
      attributes.push({
        id: attr.attributeId,
        valueId: attr.attributeValues[0].id,
        customValue: null
      })
    }
  })

  return attributes
}
