<template>
  <!-- 图片预览对话框 -->
  <el-dialog
    v-model="imagePreviewVisible"
    :title="`${currentGoodsName} - 商品图片`"
    width="90%"
    top="3vh"
    class="image-preview-dialog"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="image-preview-container" v-if="currentImageList.length > 0">
      <!-- 主图片显示区域 -->
      <div class="main-image-container">
        <el-image
          :src="currentImageList[currentImageIndex]"
          fit="contain"
          class="main-image"
          :lazy="true"
          loading="eager"
        >
          <template #placeholder>
            <div class="image-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>图片加载中...</span>
            </div>
          </template>
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>

        <!-- 图片计数器 -->
        <div class="image-counter">
          {{ currentImageIndex + 1 }} / {{ currentImageList.length }}
        </div>

        <!-- 左右切换按钮 -->
        <div class="nav-buttons" v-if="currentImageList.length > 1">
          <el-button
            class="nav-btn prev-btn"
            @click="prevImage"
            circle
            size="large"
          >
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <el-button
            class="nav-btn next-btn"
            @click="nextImage"
            circle
            size="large"
          >
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 缩略图导航 -->
      <div class="thumbnail-nav" v-if="currentImageList.length > 1">
        <div
          v-for="(image, index) in currentImageList"
          :key="index"
          class="thumbnail-item"
          :class="{ active: index === currentImageIndex }"
          @click="goToImage(index)"
        >
          <el-image
            :src="image"
            fit="cover"
            class="thumbnail-image"
          >
            <template #error>
              <div class="thumbnail-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="imagePreviewVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Loading, Picture, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
  imageList: string[]
  currentIndex: number
  goodsName: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  imageList: () => [],
  currentIndex: 0,
  goodsName: ''
})

// Events
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:currentIndex': [index: number]
}>()

// 本地状态
const imagePreviewVisible = ref(false)
const currentImageList = ref<string[]>([])
const currentImageIndex = ref(0)
const currentGoodsName = ref('')

// 监听 props 变化
watch(() => props.visible, (newValue) => {
  imagePreviewVisible.value = newValue
})

watch(() => props.imageList, (newValue) => {
  currentImageList.value = [...newValue]
})

watch(() => props.currentIndex, (newValue) => {
  currentImageIndex.value = newValue
})

watch(() => props.goodsName, (newValue) => {
  currentGoodsName.value = newValue
})

// 监听本地状态变化
watch(imagePreviewVisible, (newValue) => {
  emit('update:visible', newValue)
})

watch(currentImageIndex, (newValue) => {
  emit('update:currentIndex', newValue)
})

// 切换到上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  } else {
    currentImageIndex.value = currentImageList.value.length - 1
  }
}

// 切换到下一张图片
const nextImage = () => {
  if (currentImageIndex.value < currentImageList.value.length - 1) {
    currentImageIndex.value++
  } else {
    currentImageIndex.value = 0
  }
}

// 跳转到指定图片
const goToImage = (index: number) => {
  currentImageIndex.value = index
}
</script>

<style scoped>
/* 图片预览相关样式 */
.image-preview-dialog {
  margin-top: 3vh !important;
}

.image-preview-dialog .el-dialog {
  max-height: 94vh;
  display: flex;
  flex-direction: column;
}

.image-preview-dialog .el-dialog__header {
  flex-shrink: 0;
  padding: 15px 20px 10px;
}

.image-preview-dialog .el-dialog__body {
  padding: 0 20px 10px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.image-preview-dialog .el-dialog__footer {
  flex-shrink: 0;
  padding: 10px 20px 15px;
}

.image-preview-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  min-height: 0;
  max-height: calc(94vh - 100px);
}

.main-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 200px;
  max-height: calc(94vh - 200px);
  background: #f5f7fa;
  border-radius: 8px;
  overflow: visible;
  padding: 15px;
  box-sizing: border-box;
}

.main-image {
  max-width: 100%;
  max-height: 500px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  display: block;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-loading .el-icon {
  font-size: 48px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 48px;
}

.image-counter {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-btn:hover {
  background: white;
  transform: scale(1.1);
}

.thumbnail-nav {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
  max-height: 80px;
  overflow-y: auto;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 8px;
  flex-shrink: 0;
}

.thumbnail-item {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  color: #909399;
}

.thumbnail-error .el-icon {
  font-size: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-preview-dialog {
    margin: 0 !important;
  }

  .image-preview-dialog .el-dialog {
    width: 100% !important;
    height: 100vh !important;
    max-height: 100vh !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .thumbnail-nav {
    max-height: 60px;
  }

  .thumbnail-item {
    width: 40px;
    height: 40px;
  }
}
</style>
